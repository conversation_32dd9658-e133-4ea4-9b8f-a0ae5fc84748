package com.helios.etl;

import com.helios.etl.progress.entities.TransformationMapping;
import com.helios.etl.progress.services.TransformationMappingService;
import com.helios.etl.utils.TransformationMappingHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to demonstrate and verify transformation mapping functionality.
 * This test shows how to use the transformation mapping system to track
 * equivalents between source and target entities.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TransformationMappingTest {
    
    @Autowired
    private TransformationMappingService mappingService;
    
    @Autowired
    private TransformationMappingHelper mappingHelper;
    
    @Test
    public void testBasicMappingCreationAndRetrieval() {
        // Test case 1: Tickets.idTickets <-> IssueInformatique.oid
        Long ticketId = 12345L;
        Long issueOid = 67890L;
        
        TransformationMapping mapping = mappingService.createMapping(
            "Tickets", ticketId,
            "IssueInformatique", issueOid,
            "TicketsToIssue", null
        );
        
        assertNotNull(mapping);
        assertEquals("Tickets", mapping.getSourceEntityType());
        assertEquals(ticketId, mapping.getSourceEntityId());
        assertEquals("IssueInformatique", mapping.getTargetEntityType());
        assertEquals(issueOid, mapping.getTargetEntityOid());
        assertEquals("TicketsToIssue", mapping.getTransformationType());
        
        // Test retrieval
        Optional<Long> foundTargetOid = mappingService.findTargetOid("Tickets", ticketId);
        assertTrue(foundTargetOid.isPresent());
        assertEquals(issueOid, foundTargetOid.get());
        
        Optional<Long> foundSourceId = mappingService.findSourceId("IssueInformatique", issueOid);
        assertTrue(foundSourceId.isPresent());
        assertEquals(ticketId, foundSourceId.get());
    }
    
    @Test
    public void testContactToPersonneMapping() {
        // Test case 2: Contact.idContact <-> Personne.oid
        Long contactId = 111L;
        Long personneOid = 222L;
        
        TransformationMapping mapping = mappingService.createMapping(
            "Contact", contactId,
            "Personne", personneOid,
            "ContactToPersonne", null
        );
        
        assertNotNull(mapping);
        
        // Verify using helper
        assertTrue(mappingHelper.hasMapping("Contact", contactId));
        
        Optional<Long> targetOid = mappingHelper.findTargetOid("Contact", contactId);
        assertTrue(targetOid.isPresent());
        assertEquals(personneOid, targetOid.get());
    }
    
    @Test
    public void testMappingWithSourceKey() {
        // Test case 3: Client name mapping with source key
        String clientName = "ACME Corp";
        Long clientHashId = (long) clientName.hashCode();
        Long commanditaireOid = 333L;
        
        TransformationMapping mapping = mappingService.createMapping(
            "Client", clientHashId, clientName,
            "Commanditaire", commanditaireOid,
            "ClientToCommanditaire", null, null
        );
        
        assertNotNull(mapping);
        assertEquals(clientName, mapping.getSourceEntityKey());
        
        // Test retrieval with source key
        Optional<Long> foundOid = mappingService.findTargetOid("Client", clientHashId, clientName);
        assertTrue(foundOid.isPresent());
        assertEquals(commanditaireOid, foundOid.get());
    }
    
    @Test
    public void testMappingSuperseding() {
        // Test that creating a new mapping for the same source supersedes the old one
        Long sourceId = 999L;
        Long oldTargetOid = 1111L;
        Long newTargetOid = 2222L;
        
        // Create first mapping
        TransformationMapping firstMapping = mappingService.createMapping(
            "TestEntity", sourceId,
            "TargetEntity", oldTargetOid,
            "TestTransformation", null
        );
        
        assertEquals(TransformationMapping.MappingStatus.ACTIVE, firstMapping.getStatus());
        
        // Create second mapping for same source
        TransformationMapping secondMapping = mappingService.createMapping(
            "TestEntity", sourceId,
            "TargetEntity", newTargetOid,
            "TestTransformation", null
        );
        
        assertEquals(TransformationMapping.MappingStatus.ACTIVE, secondMapping.getStatus());
        
        // Verify the new mapping is returned
        Optional<Long> currentTarget = mappingService.findTargetOid("TestEntity", sourceId);
        assertTrue(currentTarget.isPresent());
        assertEquals(newTargetOid, currentTarget.get());
        
        // Verify history shows both mappings
        List<TransformationMapping> history = mappingService.getMappingHistory("TestEntity", sourceId);
        assertEquals(2, history.size());
    }
    
    @Test
    public void testMappingStatistics() {
        // Create several mappings
        mappingService.createMapping("Tickets", 1L, "IssueInformatique", 101L, "TicketsToIssue", null);
        mappingService.createMapping("Tickets", 2L, "IssueInformatique", 102L, "TicketsToIssue", null);
        mappingService.createMapping("Contact", 3L, "Personne", 103L, "ContactToPersonne", null);
        
        // Test statistics
        List<Object[]> stats = mappingService.getMappingStatistics();
        assertFalse(stats.isEmpty());
        
        long totalMappings = mappingService.getTotalActiveMappings();
        assertTrue(totalMappings >= 3);
        
        // Test mappings by type
        List<TransformationMapping> ticketMappings = mappingService.getMappingsByTransformationType("TicketsToIssue");
        assertTrue(ticketMappings.size() >= 2);
        
        List<TransformationMapping> contactMappings = mappingService.getMappingsByTransformationType("ContactToPersonne");
        assertTrue(contactMappings.size() >= 1);
    }
    
    @Test
    public void testHelperMethods() {
        // Create test mapping
        Long sourceId = 777L;
        Long targetOid = 888L;
        
        mappingService.createMapping(
            "TestSource", sourceId,
            "TestTarget", targetOid,
            "TestTransformation", null
        );
        
        // Test helper methods
        assertTrue(mappingHelper.hasMapping("TestSource", sourceId));
        
        Optional<Long> foundTarget = mappingHelper.findTargetOid("TestSource", sourceId);
        assertTrue(foundTarget.isPresent());
        assertEquals(targetOid, foundTarget.get());
        
        Optional<Long> foundSource = mappingHelper.findSourceId("TestTarget", targetOid);
        assertTrue(foundSource.isPresent());
        assertEquals(sourceId, foundSource.get());
        
        // Test print methods (these should not throw exceptions)
        assertDoesNotThrow(() -> mappingHelper.printMappingStatistics());
        assertDoesNotThrow(() -> mappingHelper.printMappingsForType("TestTransformation"));
        assertDoesNotThrow(() -> mappingHelper.printEquivalentFor("TestSource", sourceId));
    }
}
