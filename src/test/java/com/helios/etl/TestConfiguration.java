package com.helios.etl;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication(scanBasePackages = "com.helios.etl")
@EntityScan(basePackages = {
    "com.helios.etl.progress.entities"
})
@EnableJpaRepositories(basePackages = {
    "com.helios.etl.progress.repositories"
})
public class TestConfiguration {
    // Test configuration class for Spring Boot tests
}
