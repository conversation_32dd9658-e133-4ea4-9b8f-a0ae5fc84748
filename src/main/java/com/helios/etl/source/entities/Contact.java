package com.helios.etl.source.entities;

import java.time.LocalDateTime;

public class Contact {
    private int id;
    private String nom = "";
    private String prenom = "";
    private String civilite = "";
    private String email = "";
    private String telephone = "";
    private String telephone2 = "";
    private String telephone3 = "";
    private String mobile = "";
    private String fonction = "";
    private String ctNum = "";
    private String ctNo = "";
    private LocalDateTime dateCreation = LocalDateTime.MIN;
    private LocalDateTime dateModification = LocalDateTime.MIN;
    private String note = "";
    private boolean systematique = false;
    private boolean facturation = false;
    private boolean relance = false;

    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getNom() { return nom; }
    public void setNom(String nom) { this.nom = nom; }

    public String getPrenom() { return prenom; }
    public void setPrenom(String prenom) { this.prenom = prenom; }

    public String getCivilite() { return civilite; }
    public void setCivilite(String civilite) { this.civilite = civilite; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getTelephone() { return telephone; }
    public void setTelephone(String telephone) { this.telephone = telephone; }

    public String getTelephone2() { return telephone2; }
    public void setTelephone2(String telephone2) { this.telephone2 = telephone2; }

    public String getTelephone3() { return telephone3; }
    public void setTelephone3(String telephone3) { this.telephone3 = telephone3; }

    public String getMobile() { return mobile; }
    public void setMobile(String mobile) { this.mobile = mobile; }

    public String getFonction() { return fonction; }
    public void setFonction(String fonction) { this.fonction = fonction; }

    public String getCtNum() { return ctNum; }
    public void setCtNum(String ctNum) { this.ctNum = ctNum; }

    public String getCtNo() { return ctNo; }
    public void setCtNo(String ctNo) { this.ctNo = ctNo; }

    public LocalDateTime getDateCreation() { return dateCreation; }
    public void setDateCreation(LocalDateTime dateCreation) { this.dateCreation = dateCreation; }

    public LocalDateTime getDateModification() { return dateModification; }
    public void setDateModification(LocalDateTime dateModification) { this.dateModification = dateModification; }

    public String getNote() { return note; }
    public void setNote(String note) { this.note = note; }

    public boolean isSystematique() { return systematique; }
    public void setSystematique(boolean systematique) { this.systematique = systematique; }

    public boolean isFacturation() { return facturation; }
    public void setFacturation(boolean facturation) { this.facturation = facturation; }

    public boolean isRelance() { return relance; }
    public void setRelance(boolean relance) { this.relance = relance; }
}