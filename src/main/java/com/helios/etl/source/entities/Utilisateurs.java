package com.helios.etl.source.entities;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Utilisateurs {
    private Integer idUtilisateur;
    private String utilisateur;
    private String societe;
    private String nom;
    private String prenom;
    private String email;
    private String telephone;
    private String mdp;
    private Integer idPole;
    private String multiPole;
    private Integer groupe;
    private String service;
    private Boolean terrain;
    private Boolean admin;
    private LocalDateTime derniereConnexion;
    private Boolean actif;
    private Boolean droitContact;
    private Boolean droitPret;
    private Boolean droitContrat;
    private Boolean droitTemps;
    private Boolean droitModele;
    private Boolean droitUtilisateur;
    private Boolean droitReponse;
    private Boolean droitWildix;
    private Boolean droitCommande;
    private Boolean droitObjectif;
    private Boolean droitPole;
    private Boolean droitQualiopi;
    private Boolean droitQualiteSaisie;
    private Boolean droitPlanification;
    private Boolean droitPieces;
    private Boolean suiviMail;
    private String cheminCommandePJ;
    private Integer droitInfogerances;
    private Boolean droitADV;
    private Boolean droitAbbot;
    private Boolean droitLicenceLogiciel;
    private Boolean droitDemandeBureautique;
}