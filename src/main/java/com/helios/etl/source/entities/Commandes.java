package com.helios.etl.source.entities;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Commandes {
    private int idCommandes;
    private String ctNum = "";
    private String client = "";
    private String commande = "";
    private int idTickets;
    private String pole = "";
    private String commercial = "";
    private String titre = "";
    private String contactNom = "";
    private String contactTel = "";
    private String contactEmail = "";
    private String adresse = "";
    private LocalDateTime dateLivraison = LocalDateTime.MIN;
    private boolean respectDateLivraison = false;
    private String type = "";
    private boolean planification = false;
    private String note = "";
    private int bailleur;
    private String facture = "";
    private String tech1 = "";
    private String tech2 = "";
    private LocalDateTime dateFin = LocalDateTime.MIN;
    private boolean majAbonne = false;
    private boolean suiviMail = false;
    private LocalDateTime dateDemandePlanif = LocalDateTime.MIN;
    private boolean advCheck = false;
    private boolean advUpdate = false;
    private String advNote = "";


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Commandes{\n");
        sb.append("  idCommandes=").append(idCommandes).append("\n");
        sb.append("  ctNum='").append(ctNum).append("'\n");
        sb.append("  client='").append(client).append("'\n");
        sb.append("  commande='").append(commande).append("'\n");
        sb.append("  idTickets=").append(idTickets).append("\n");
        sb.append("  pole='").append(pole).append("'\n");
        sb.append("  commercial='").append(commercial).append("'\n");
        sb.append("  titre='").append(titre).append("'\n");
        sb.append("  contactNom='").append(contactNom).append("'\n");
        sb.append("  contactTel='").append(contactTel).append("'\n");
        sb.append("  contactEmail='").append(contactEmail).append("'\n");
        sb.append("  adresse='").append(adresse).append("'\n");
        sb.append("  dateLivraison=").append(dateLivraison).append("\n");
        sb.append("  respectDateLivraison=").append(respectDateLivraison).append("\n");
        sb.append("  type='").append(type).append("'\n");
        sb.append("  planification=").append(planification).append("\n");
        sb.append("  bailleur=").append(bailleur).append("\n");
        sb.append("  facture='").append(facture).append("'\n");
        sb.append("  tech1='").append(tech1).append("'\n");
        sb.append("  tech2='").append(tech2).append("'\n");
        sb.append("  dateFin=").append(dateFin).append("\n");
        sb.append("  majAbonne=").append(majAbonne).append("\n");
        sb.append("  suiviMail=").append(suiviMail).append("\n");
        sb.append("  dateDemandePlanif=").append(dateDemandePlanif).append("\n");
        sb.append("  advCheck=").append(advCheck).append("\n");
        sb.append("  advUpdate=").append(advUpdate).append("\n");
        sb.append("  note='").append(note).append("'\n");
        sb.append("  advNote='").append(advNote).append("'\n");
        sb.append("}");
        return sb.toString();
    }
}