package com.helios.etl.source.entities;

import java.time.LocalDateTime;

public class Tickets {
    private int idTickets;
    private String ctNum = "";
    private String client = "";
    private LocalDateTime dateCreation = LocalDateTime.MIN;
    private String demandeur = "";
    private String assigne = "";
    private String pole = "";
    private String type = "";
    private String status = "";
    private String priorite = "";
    private int niveau;
    private String categorie = "";
    private String categorie2 = "";
    private String categorie3 = "";
    private int tempsTotal;
    private String titre = "";
    private String description = "";
    private LocalDateTime dateRappel = LocalDateTime.MIN;
    private LocalDateTime dateResolution = LocalDateTime.MIN;
    private int statusTemps;
    private int avertissement;
    private String dernierCorrespondant = "";
    private LocalDateTime datePremiereReponse = LocalDateTime.MIN;
    private LocalDateTime dateDerniereReponse = LocalDateTime.MIN;
    private boolean notificationEnabled = false;

    public int getIdTickets() { return idTickets; }
    public void setIdTickets(int idTickets) { this.idTickets = idTickets; }

    public String getCtNum() { return ctNum; }
    public void setCtNum(String ctNum) { this.ctNum = ctNum; }

    public String getClient() { return client; }
    public void setClient(String client) { this.client = client; }

    public LocalDateTime getDateCreation() { return dateCreation; }
    public void setDateCreation(LocalDateTime dateCreation) { this.dateCreation = dateCreation; }

    public String getDemandeur() { return demandeur; }
    public void setDemandeur(String demandeur) { this.demandeur = demandeur; }

    public String getAssigne() { return assigne; }
    public void setAssigne(String assigne) { this.assigne = assigne; }

    public String getPole() { return pole; }
    public void setPole(String pole) { this.pole = pole; }

    public String getType() { return type; }
    public void setType(String type) { this.type = type; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getPriorite() { return priorite; }
    public void setPriorite(String priorite) { this.priorite = priorite; }

    public int getNiveau() { return niveau; }
    public void setNiveau(int niveau) { this.niveau = niveau; }

    public String getCategorie() { return categorie; }
    public void setCategorie(String categorie) { this.categorie = categorie; }

    public String getCategorie2() { return categorie2; }
    public void setCategorie2(String categorie2) { this.categorie2 = categorie2; }

    public String getCategorie3() { return categorie3; }
    public void setCategorie3(String categorie3) { this.categorie3 = categorie3; }

    public int getTempsTotal() { return tempsTotal; }
    public void setTempsTotal(int tempsTotal) { this.tempsTotal = tempsTotal; }

    public String getTitre() { return titre; }
    public void setTitre(String titre) { this.titre = titre; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public LocalDateTime getDateRappel() { return dateRappel; }
    public void setDateRappel(LocalDateTime dateRappel) { this.dateRappel = dateRappel; }

    public LocalDateTime getDateResolution() { return dateResolution; }
    public void setDateResolution(LocalDateTime dateResolution) { this.dateResolution = dateResolution; }

    public int getStatusTemps() { return statusTemps; }
    public void setStatusTemps(int statusTemps) { this.statusTemps = statusTemps; }

    public int getAvertissement() { return avertissement; }
    public void setAvertissement(int avertissement) { this.avertissement = avertissement; }

    public String getDernierCorrespondant() { return dernierCorrespondant; }
    public void setDernierCorrespondant(String dernierCorrespondant) { this.dernierCorrespondant = dernierCorrespondant; }

    public LocalDateTime getDatePremiereReponse() { return datePremiereReponse; }
    public void setDatePremiereReponse(LocalDateTime datePremiereReponse) { this.datePremiereReponse = datePremiereReponse; }

    public LocalDateTime getDateDerniereReponse() { return dateDerniereReponse; }
    public void setDateDerniereReponse(LocalDateTime dateDerniereReponse) { this.dateDerniereReponse = dateDerniereReponse; }

    public boolean isNotificationEnabled() { return notificationEnabled; }
    public void setNotificationEnabled(boolean notificationEnabled) { this.notificationEnabled = notificationEnabled; }
}