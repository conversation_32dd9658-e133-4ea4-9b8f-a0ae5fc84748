package com.helios.etl.source.entities;

import java.time.LocalDateTime;

public class TicketsHistorique {
    private int idHistorique;
    private int idTickets;
    private LocalDateTime dateModification = LocalDateTime.MIN;
    private String correspondant = "";
    private String description = "";
    private int noteInterne;
    private int pieceJointe;
    private int envoiEmail;
    private int noteType;
    private int temps;

    public int getIdHistorique() { return idHistorique; }
    public void setIdHistorique(int idHistorique) { this.idHistorique = idHistorique; }

    public int getIdTickets() { return idTickets; }
    public void setIdTickets(int idTickets) { this.idTickets = idTickets; }

    public LocalDateTime getDateModification() { return dateModification; }
    public void setDateModification(LocalDateTime dateModification) { this.dateModification = dateModification; }

    public String getCorrespondant() { return correspondant; }
    public void setCorrespondant(String correspondant) { this.correspondant = correspondant; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public int getNoteInterne() { return noteInterne; }
    public void setNoteInterne(int noteInterne) { this.noteInterne = noteInterne; }

    public int getPieceJointe() { return pieceJointe; }
    public void setPieceJointe(int pieceJointe) { this.pieceJointe = pieceJointe; }

    public int getEnvoiEmail() { return envoiEmail; }
    public void setEnvoiEmail(int envoiEmail) { this.envoiEmail = envoiEmail; }

    public int getNoteType() { return noteType; }
    public void setNoteType(int noteType) { this.noteType = noteType; }

    public int getTemps() { return temps; }
    public void setTemps(int temps) { this.temps = temps; }
}