
package com.helios.etl.source.repositories;

import com.helios.etl.source.entities.Utilisateurs;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public class UtilisateursRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public UtilisateursRepository(@Qualifier("sourceSqlServerJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private final RowMapper<Utilisateurs> rowMapper = new RowMapper<Utilisateurs>() {
        @Override
        public Utilisateurs mapRow(ResultSet rs, int rowNum) throws SQLException {
            Utilisateurs u = new Utilisateurs();
            u.setIdUtilisateur(rs.getInt("idUtilisateur"));
            u.setUtilisateur(rs.getString("utilisateur"));
            u.setSociete(rs.getString("societe"));
            u.setNom(rs.getString("nom"));
            u.setPrenom(rs.getString("prenom"));
            u.setEmail(rs.getString("email"));
            u.setTelephone(rs.getString("telephone"));
            u.setMdp(rs.getString("mdp"));
            u.setIdPole(rs.getInt("idPole"));
            u.setMultiPole(rs.getString("multiPole"));
            u.setGroupe(rs.getInt("groupe"));
            u.setService(rs.getString("service"));
            u.setTerrain(rs.getBoolean("terrain"));
            u.setAdmin(rs.getBoolean("admin"));
            u.setDerniereConnexion(getSafeDateTime(rs, "derniereConnexion"));
            u.setActif(rs.getBoolean("actif"));
            u.setDroitContact(rs.getBoolean("droitContact"));
            u.setDroitPret(rs.getBoolean("droitPret"));
            u.setDroitContrat(rs.getBoolean("droitContrat"));
            u.setDroitTemps(rs.getBoolean("droitTemps"));
            u.setDroitModele(rs.getBoolean("droitModele"));
            u.setDroitUtilisateur(rs.getBoolean("droitUtilisateur"));
            u.setDroitReponse(rs.getBoolean("droitReponse"));
            u.setDroitWildix(rs.getBoolean("droitWildix"));
            u.setDroitCommande(rs.getBoolean("droitCommande"));
            u.setDroitObjectif(rs.getBoolean("droitObjectif"));
            u.setDroitPole(rs.getBoolean("droitPole"));
            u.setDroitQualiopi(rs.getBoolean("droitQualiopi"));
            u.setDroitQualiteSaisie(rs.getBoolean("droitQualiteSaisie"));
            u.setDroitPlanification(rs.getBoolean("droitPlanification"));
            u.setDroitPieces(rs.getBoolean("droitPieces"));
            u.setSuiviMail(rs.getBoolean("suiviMail"));
            u.setCheminCommandePJ(rs.getString("cheminCommandePJ"));
            u.setDroitInfogerances(rs.getInt("droitInfogerances"));
            u.setDroitADV(rs.getBoolean("droitADV"));
            u.setDroitAbbot(rs.getBoolean("droitAbbot"));
            u.setDroitLicenceLogiciel(rs.getBoolean("droitLicenceLogiciel"));
            u.setDroitDemandeBureautique(rs.getBoolean("droitDemandeBureautique"));
            return u;
        }

        private LocalDateTime getSafeDateTime(ResultSet rs, String column) throws SQLException {
            Timestamp ts = rs.getTimestamp(column);
            return ts != null ? ts.toLocalDateTime() : LocalDateTime.MIN;
        }
    };

    public List<Utilisateurs> getAll() {
        String sql = "SELECT * FROM Utilisateurs";
        return jdbcTemplate.query(sql, rowMapper);
    }

    public Utilisateurs getById(int id) {
        String sql = "SELECT * FROM Utilisateurs WHERE idUtilisateur = ?";
        List<Utilisateurs> result = jdbcTemplate.query(sql, rowMapper, id);
        return result.isEmpty() ? null : result.get(0);
    }

    /**
     * Retrieve a user by their username or full name.
     * This method checks both the 'utilisateur' field and the concatenation of 'nom' and 'prenom'.
     * @param username
     * @return Utilisateurs object if found, otherwise null.
     */
    public Utilisateurs getByUsername(String username) {
        if(username == null || username.isEmpty()) {
            return null;
        }

        String sql = "SELECT * FROM Utilisateurs WHERE utilisateur = ? OR CONCAT(nom, ' ', prenom) = ?";
        List<Utilisateurs> result = jdbcTemplate.query(sql, rowMapper, username, username);
        return result.isEmpty() ? null : result.get(0);
    }

    public void add(Utilisateurs entity) {
        String sql = "INSERT INTO Utilisateurs (utilisateur, societe, nom, prenom, email, telephone, mdp, idPole, multiPole, groupe, service, terrain, admin, derniereConnexion, actif, droitContact, droitPret, droitContrat, droitTemps, droitModele, droitUtilisateur, droitReponse, droitWildix, droitCommande, droitObjectif, droitPole, droitQualiopi, droitQualiteSaisie, droitPlanification, droitPieces, suiviMail, cheminCommandePJ, droitInfogerances, droitADV, droitAbbot, droitLicenceLogiciel, droitDemandeBureautique) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        jdbcTemplate.update(sql,
                entity.getUtilisateur(),
                entity.getSociete(),
                entity.getNom(),
                entity.getPrenom(),
                entity.getEmail(),
                entity.getTelephone(),
                entity.getMdp(),
                entity.getIdPole(),
                entity.getMultiPole(),
                entity.getGroupe(),
                entity.getService(),
                entity.getTerrain(),
                entity.getAdmin(),
                entity.getDerniereConnexion(),
                entity.getActif(),
                entity.getDroitContact(),
                entity.getDroitPret(),
                entity.getDroitContrat(),
                entity.getDroitTemps(),
                entity.getDroitModele(),
                entity.getDroitUtilisateur(),
                entity.getDroitReponse(),
                entity.getDroitWildix(),
                entity.getDroitCommande(),
                entity.getDroitObjectif(),
                entity.getDroitPole(),
                entity.getDroitQualiopi(),
                entity.getDroitQualiteSaisie(),
                entity.getDroitPlanification(),
                entity.getDroitPieces(),
                entity.getSuiviMail(),
                entity.getCheminCommandePJ(),
                entity.getDroitInfogerances(),
                entity.getDroitADV(),
                entity.getDroitAbbot(),
                entity.getDroitLicenceLogiciel(),
                entity.getDroitDemandeBureautique()
        );
    }

    public void update(Utilisateurs entity) {
        String sql = "UPDATE Utilisateurs SET utilisateur = ?, societe = ?, nom = ?, prenom = ?, email = ?, telephone = ?, mdp = ?, idPole = ?, multiPole = ?, groupe = ?, service = ?, terrain = ?, admin = ?, derniereConnexion = ?, actif = ?, droitContact = ?, droitPret = ?, droitContrat = ?, droitTemps = ?, droitModele = ?, droitUtilisateur = ?, droitReponse = ?, droitWildix = ?, droitCommande = ?, droitObjectif = ?, droitPole = ?, droitQualiopi = ?, droitQualiteSaisie = ?, droitPlanification = ?, droitPieces = ?, suiviMail = ?, cheminCommandePJ = ?, droitInfogerances = ?, droitADV = ?, droitAbbot = ?, droitLicenceLogiciel = ?, droitDemandeBureautique = ? WHERE idUtilisateur = ?";
        jdbcTemplate.update(sql,
                entity.getUtilisateur(),
                entity.getSociete(),
                entity.getNom(),
                entity.getPrenom(),
                entity.getEmail(),
                entity.getTelephone(),
                entity.getMdp(),
                entity.getIdPole(),
                entity.getMultiPole(),
                entity.getGroupe(),
                entity.getService(),
                entity.getTerrain(),
                entity.getAdmin(),
                entity.getDerniereConnexion(),
                entity.getActif(),
                entity.getDroitContact(),
                entity.getDroitPret(),
                entity.getDroitContrat(),
                entity.getDroitTemps(),
                entity.getDroitModele(),
                entity.getDroitUtilisateur(),
                entity.getDroitReponse(),
                entity.getDroitWildix(),
                entity.getDroitCommande(),
                entity.getDroitObjectif(),
                entity.getDroitPole(),
                entity.getDroitQualiopi(),
                entity.getDroitQualiteSaisie(),
                entity.getDroitPlanification(),
                entity.getDroitPieces(),
                entity.getSuiviMail(),
                entity.getCheminCommandePJ(),
                entity.getDroitInfogerances(),
                entity.getDroitADV(),
                entity.getDroitAbbot(),
                entity.getDroitLicenceLogiciel(),
                entity.getDroitDemandeBureautique(),
                entity.getIdUtilisateur()
        );
    }

    public void delete(Utilisateurs entity) {
        String sql = "DELETE FROM Utilisateurs WHERE idUtilisateur = ?";
        jdbcTemplate.update(sql, entity.getIdUtilisateur());
    }
}