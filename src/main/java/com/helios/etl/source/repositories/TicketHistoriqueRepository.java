package com.helios.etl.source.repositories;

import com.helios.etl.source.entities.TicketsHistorique;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;

@Repository
public class TicketHistoriqueRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public TicketHistoriqueRepository(@Qualifier("sourceSqlServerJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private final RowMapper<TicketsHistorique> rowMapper = new RowMapper<TicketsHistorique>() {
        @Override
        public TicketsHistorique mapRow(ResultSet rs, int rowNum) throws SQLException {
            TicketsHistorique t = new TicketsHistorique();
            t.setIdHistorique(rs.getInt("idHistorique"));
            t.setIdTickets(rs.getInt("idTickets"));
            t.setDateModification(getSafeDateTime(rs, "dateModification"));
            t.setCorrespondant(rs.getString("correspondant"));
            t.setDescription(rs.getString("description"));
            t.setNoteInterne(rs.getInt("noteInterne"));
            t.setPieceJointe(rs.getInt("pieceJointe"));
            t.setEnvoiEmail(rs.getInt("envoiEmail"));
            t.setNoteType(rs.getInt("noteType"));
            t.setTemps(rs.getInt("temps"));
            return t;
        }

        private LocalDateTime getSafeDateTime(ResultSet rs, String column) throws SQLException {
            Timestamp ts = rs.getTimestamp(column);
            return ts != null ? ts.toLocalDateTime() : LocalDateTime.MIN;
        }
    };

    /**
     * Retrieve all Ticket Historique entries modified in the last 2 years.
     * @return List of TicketsHistorique
     */
    public List<TicketsHistorique> getAll() {
        String sql = "SELECT th.* FROM TicketsHistorique th " +
                "JOIN Tickets t ON th.idTickets = t.idTickets " +
                "WHERE th.dateModification > DATEADD(year, -2, GETDATE()) " +
                "AND t.status <> 'Inactif' " +
                "AND t.status = N'Résolu' " +
                "ORDER BY th.dateModification DESC, th.idTickets DESC";
        return jdbcTemplate.query(sql, rowMapper);
    }

    public TicketsHistorique getById(int id) {
        String sql = "SELECT * FROM TicketsHistorique WHERE idHistorique = ?";
        List<TicketsHistorique> result = jdbcTemplate.query(sql, rowMapper, id);
        return result.isEmpty() ? null : result.get(0);
    }

    public TicketsHistorique getLastHistoriqueFromTicketId(int id) {
        String sql = "SELECT TOP 1 * FROM TicketsHistorique WHERE idTickets = ? AND noteType <> 8 ORDER BY idHistorique DESC";
        List<TicketsHistorique> result = jdbcTemplate.query(sql, rowMapper, id);
        return result.isEmpty() ? null : result.get(0);
    }

    public void add(TicketsHistorique entity) {
        String sql = "INSERT INTO TicketsHistorique (idTickets, dateModification, correspondant, description, noteInterne, pieceJointe, envoiEmail, noteType, temps) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        jdbcTemplate.update(sql,
                entity.getIdTickets(),
                entity.getDateModification(),
                entity.getCorrespondant(),
                entity.getDescription(),
                entity.getNoteInterne(),
                entity.getPieceJointe(),
                entity.getEnvoiEmail(),
                entity.getNoteType(),
                entity.getTemps()
        );
    }

    public void update(TicketsHistorique entity) {
        String sql = "UPDATE TicketsHistorique SET idTickets = ?, dateModification = ?, correspondant = ?, description = ?, noteInterne = ?, pieceJointe = ?, envoiEmail = ?, noteType = ?, temps = ? WHERE idHistorique = ?";
        jdbcTemplate.update(sql,
                entity.getIdTickets(),
                entity.getDateModification(),
                entity.getCorrespondant(),
                entity.getDescription(),
                entity.getNoteInterne(),
                entity.getPieceJointe(),
                entity.getEnvoiEmail(),
                entity.getNoteType(),
                entity.getTemps(),
                entity.getIdHistorique()
        );
    }

    public void delete(TicketsHistorique entity) {
        String sql = "DELETE FROM TicketsHistorique WHERE idHistorique = ?";
        jdbcTemplate.update(sql, entity.getIdHistorique());
    }

    public HashSet<TicketsHistorique> getAllHistoriqueFromTicketId(int idTickets) {
        String sql = "SELECT * FROM TicketsHistorique WHERE idTickets = ? ORDER BY dateModification DESC";
        List<TicketsHistorique> result = jdbcTemplate.query(sql, rowMapper, idTickets);

        if (result.isEmpty()) {
            return new HashSet<>();
        }

        return new HashSet<>(result);
    }
}