package com.helios.etl.source.repositories;

import com.helios.etl.source.entities.Contact;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;

@Repository
public class ContactsRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public ContactsRepository(@Qualifier("sourceSqlServerJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private final RowMapper<Contact> rowMapper = new RowMapper<Contact>() {
        @Override
        public Contact mapRow(ResultSet rs, int rowNum) throws SQLException {
            Contact c = new Contact();
            c.setId(rs.getInt("idContacts"));
            c.setNom(rs.getString("nom"));
            c.setPrenom(rs.getString("prenom"));
            c.setCivilite(rs.getString("civilite"));
            c.setEmail(rs.getString("email"));
            c.setTelephone(rs.getString("telephone"));
            c.setTelephone2(rs.getString("telephone2"));
            c.setTelephone3(rs.getString("telephone3"));
            c.setMobile(rs.getString("mobile"));
            c.setFonction(rs.getString("fonction"));
            c.setCtNum(rs.getString("ct_num"));
            c.setCtNo(rs.getString("ct_no"));
            c.setDateCreation(getSafeDateTime(rs, "dateCreation"));
            c.setDateModification(getSafeDateTime(rs, "dateModification"));
            c.setNote(rs.getString("note"));
            c.setSystematique(rs.getBoolean("systematique"));
            c.setFacturation(rs.getBoolean("facturation"));
            c.setRelance(rs.getBoolean("relance"));
            return c;
        }

        private LocalDateTime getSafeDateTime(ResultSet rs, String column) throws SQLException {
            Timestamp ts = rs.getTimestamp(column);
            return ts != null ? ts.toLocalDateTime() : LocalDateTime.MIN;
        }
    };

    public List<Contact> getAll() {
        String sql = "SELECT * FROM Contacts";
        return jdbcTemplate.query(sql, rowMapper);
    }

    public Contact getById(int id) {
        String sql = "SELECT * FROM Contacts WHERE idContacts = ?";
        List<Contact> result = jdbcTemplate.query(sql, rowMapper, id);
        return result.isEmpty() ? null : result.get(0);
    }

    public void add(Contact entity) {
        String sql = "INSERT INTO Contacts (nom, prenom, civilite, email, telephone, telephone2, telephone3, mobile, fonction, ct_num, ct_no, dateCreation, dateModification, note, systematique, facturation, relance) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        jdbcTemplate.update(sql,
                entity.getNom(),
                entity.getPrenom(),
                entity.getCivilite(),
                entity.getEmail(),
                entity.getTelephone(),
                entity.getTelephone2(),
                entity.getTelephone3(),
                entity.getMobile(),
                entity.getFonction(),
                entity.getCtNum(),
                entity.getCtNo(),
                entity.getDateCreation(),
                entity.getDateModification(),
                entity.getNote(),
                entity.isSystematique(),
                entity.isFacturation(),
                entity.isRelance()
        );
    }

    public void update(Contact entity) {
        String sql = "UPDATE Contacts SET nom = ?, prenom = ?, civilite = ?, email = ?, telephone = ?, telephone2 = ?, telephone3 = ?, mobile = ?, fonction = ?, ct_num = ?, ct_no = ?, dateCreation = ?, dateModification = ?, note = ?, systematique = ?, facturation = ?, relance = ? WHERE idContacts = ?";
        jdbcTemplate.update(sql,
                entity.getNom(),
                entity.getPrenom(),
                entity.getCivilite(),
                entity.getEmail(),
                entity.getTelephone(),
                entity.getTelephone2(),
                entity.getTelephone3(),
                entity.getMobile(),
                entity.getFonction(),
                entity.getCtNum(),
                entity.getCtNo(),
                entity.getDateCreation(),
                entity.getDateModification(),
                entity.getNote(),
                entity.isSystematique(),
                entity.isFacturation(),
                entity.isRelance(),
                entity.getId()
        );
    }

    public void delete(Contact entity) {
        String sql = "DELETE FROM Contacts WHERE idContacts = ?";
        jdbcTemplate.update(sql, entity.getId());
    }

    public Contact getByFullName(String demandeur) {
    if (demandeur == null || demandeur.isEmpty()) {
            return null;
        }

        String sql = "SELECT * FROM Contacts WHERE CONCAT(nom, ' ', prenom) = ?";
        List<Contact> result = jdbcTemplate.query(sql, rowMapper, demandeur);
        return result.isEmpty() ? null : result.get(0);
    }

    public HashSet<Contact> getAllByCtNum(String ctNum) {
        if (ctNum == null || ctNum.isEmpty()) {
            return new HashSet<>();
        }

        String sql = "SELECT * FROM Contacts WHERE ct_num = ?";
        List<Contact> result = jdbcTemplate.query(sql, rowMapper, ctNum);

        if (result.isEmpty()) {
            return new HashSet<>();
        }

        return new HashSet<>(result);
    }
}