package com.helios.etl.source.repositories;

import com.helios.etl.source.entities.Commandes;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Repository
public class CommandesRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public CommandesRepository(@Qualifier("sourceSqlServerJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private final RowMapper<Commandes> rowMapper = new RowMapper<Commandes>() {
        @Override
        public Commandes mapRow(ResultSet rs, int rowNum) throws SQLException {
            Commandes c = new Commandes();
            c.setIdCommandes(rs.getInt("IdCommandes"));
            c.setCtNum(rs.getString("ct_num"));
            c.setClient(rs.getString("client"));
            c.setCommande(rs.getString("commande"));
            c.setIdTickets(rs.getInt("idTickets"));
            c.setPole(rs.getString("pole"));
            c.setCommercial(rs.getString("commercial"));
            c.setTitre(rs.getString("titre"));
            c.setContactNom(rs.getString("contactNom"));
            c.setContactTel(rs.getString("contactTel"));
            c.setContactEmail(rs.getString("contactEmail"));
            c.setAdresse(rs.getString("adresse"));
            c.setDateLivraison(getSafeDateTime(rs, "dateLivraison"));
            c.setRespectDateLivraison(rs.getBoolean("respectLivraison"));
            c.setType(rs.getString("type"));
            c.setPlanification(rs.getBoolean("planification"));
            c.setNote(rs.getString("note"));
            c.setBailleur(rs.getInt("bailleur"));
            c.setFacture(rs.getString("facture"));
            c.setTech1(rs.getString("tech1"));
            c.setTech2(rs.getString("tech2"));
            c.setDateFin(getSafeDateTime(rs, "dateFin"));
            c.setMajAbonne(rs.getBoolean("majAbo"));
            c.setSuiviMail(rs.getBoolean("suiviMail"));
            c.setDateDemandePlanif(getSafeDateTime(rs, "dateDemandePlanif"));
            c.setAdvCheck(rs.getBoolean("adv_check"));
            c.setAdvUpdate(rs.getBoolean("adv_update"));
            c.setAdvNote(rs.getString("adv_note"));
            return c;
        }

        private LocalDateTime getSafeDateTime(ResultSet rs, String column) throws SQLException {
            java.sql.Timestamp ts = rs.getTimestamp(column);
            return ts != null ? ts.toLocalDateTime() : LocalDateTime.MIN;
        }
    };

    public Set<Commandes> getAll() {
        String sql = "SELECT * FROM Commandes";
        List<Commandes> list = jdbcTemplate.query(sql, rowMapper);
        return new HashSet<>(list);
    }

    public Commandes getById(int id) {
        String sql = "SELECT * FROM Commandes WHERE IdCommandes = ?";
        List<Commandes> result = jdbcTemplate.query(sql, rowMapper, id);
        return result.isEmpty() ? null : result.get(0);
    }

    public void add(Commandes entity) {
        throw new UnsupportedOperationException();
    }

    public void update(Commandes entity) {
        throw new UnsupportedOperationException();
    }

    public void delete(Commandes entity) {
        throw new UnsupportedOperationException();
    }
}