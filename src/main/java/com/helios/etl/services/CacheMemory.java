package com.helios.etl.services;

import com.helios.etl.model.*;
import com.helios.etl.progress.entities.EtlRun;
import com.helios.etl.progress.repositories.EtlRunRepository;
import com.helios.etl.repository.*;
import com.helios.etl.source.entities.*;
import com.helios.etl.source.repositories.*;
import com.helios.etl.utils.CacheMemoryHelper;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;

@Data
@Service
public class CacheMemory {
    private static final Logger log = LoggerFactory.getLogger(CacheMemory.class);
    private EtlRun etlRun;

    private CacheMemoryHelper cacheMemoryHelper = null;
    public CacheMemoryHelper getHelper() {
        if(this.cacheMemoryHelper == null)
            this.cacheMemoryHelper = new CacheMemoryHelper(this);

        return this.cacheMemoryHelper;
    }

    @Autowired
    private EtlRunRepository etlRunRepository;

    //V1 (source repositories)
    @Autowired
    private TicketsRepository ticketsRepository;
    @Autowired
    private TicketHistoriqueRepository ticketHistoriqueRepository;
    @Autowired
    private ContactsRepository contactsRepository;
    @Autowired
    private CommandesRepository commandesRepository;
    @Autowired
    private CategoriesRepository categoriesRepository;
    @Autowired
    private UtilisateursRepository utilisateursRepository;

    //V2 (target JPA repositories)
    @Autowired
    private CommanditaireRepository commanditaireRepository;
    @Autowired
    private DocumentContractuelRepository documentContractuelRepository;
    @Autowired
    private DomaineMetierRepository domaineMetierRepository;
    @Autowired
    private IssueInformatiqueRepository issueInformatiqueRepository;
    @Autowired
    private IssueOrigineRepository issueOrigineRepository;
    @Autowired
    private IssuePieceJointeRepository issuePieceJointeRepository;
    @Autowired
    private IssuePrioriteRepository issuePrioriteRepository;
    @Autowired
    private IssueRelationRepository issueRelationRepository;
    @Autowired
    private IssueStatutRepository issueStatutRepository;
    @Autowired
    private JournalDetailsRepository journalDetailsRepository;
    @Autowired
    private JournalRepository journalRepository;
    @Autowired
    private MissionRepository missionRepository;
    @Autowired
    private NiveauComplexiteRepository niveauComplexiteRepository;
    @Autowired
    private PersonneRepository personneRepository;
    @Autowired
    private ProjetRepository projetRepository;
    @Autowired
    private TypeDocumentRepository typeDocumentRepository;
    @Autowired
    private TypeMissionRepository typeMissionRepository;
    @Autowired
    private TypePersonneRepository typePersonneRepository;
    @Autowired
    private TypeProjetRepository typeProjetRepository;

    //V1 cached data
    private HashSet<Tickets> tickets;
    private HashSet<TicketsHistorique> ticketsHistorique;
    private HashSet<Contact> contacts;
    private HashSet<Commandes> commandes;
    private HashSet<Categorie> categories;
    private HashSet<Utilisateurs> utilisateurs;

    //V2 cached data
    private HashSet<AbstractListeValeurs> abstractListeValeurs;
    private HashSet<Commanditaire> commanditaires;
    private HashSet<DocumentContractuel> documentContractuels;
    private HashSet<DomaineMetier> domaineMetiers;
    private HashSet<IssueInformatique> issueInformatiques;
    private HashSet<IssueOrigine> issueOrigines;
    private HashSet<IssuePieceJointe> issuePieceJointes;
    private HashSet<IssuePriorite> issuePriorites;
    private HashSet<IssueRelation> issueRelations;
    private HashSet<IssueStatut> issueStatuts;
    private HashSet<JournalDetails> journalDetails;
    private HashSet<Journal> journaux;
    private HashSet<Mission> missions;
    private HashSet<NiveauComplexite> niveauComplexites;
    private HashSet<Personne> personnes;
    private HashSet<Projet> projets;
    private HashSet<TypeDocument> typeDocuments;
    private HashSet<TypeMission> typeMissions;
    private HashSet<TypePersonne> typePersonnes;
    private HashSet<TypeProjet> typeProjets;

    @PostConstruct
    public void init() {

        cacheMemoryHelper = new CacheMemoryHelper(this);

        //IF RUN IS DEBUG OR TEST, DO NOT LOAD DATA
        boolean DEBUG = true;
        if(DEBUG) {
            log.warn("Running in DEBUG mode, CacheMemory will not load data from repositories.");
            this.initializeEmptyCollections();
            return;
        }

        try {
            log.info("Initializing CacheMemory...");
            this.etlRun = this.initEtlRun();

            // V1 - Load source data with error handling
            loadSourceData();

            // V2 - Load target data with error handling
            loadTargetData();

            log.info("CacheMemory initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize CacheMemory completely: {}", e.getMessage());
            // Initialize empty collections to prevent null pointer exceptions
            initializeEmptyCollections();
        }
    }

    private void loadSourceData() {
        try {
            log.info("Starting to load source data...");

            log.info("Loading Utilisateurs...");
            if (utilisateursRepository != null) {
                utilisateurs = new HashSet<>(utilisateursRepository.getAll());
                log.info("Loaded {} utilisateurs from source", utilisateurs.size());
            } else {
                utilisateurs = new HashSet<>();
                log.warn("UtilisateursRepository is null, initialized empty collection");
            }

            log.info("Loading Tickets...");
            if (ticketsRepository != null) {
                tickets = new HashSet<>(ticketsRepository.getAll());
                log.info("Loaded {} tickets from source", tickets.size());
                if(this.etlRun != null)
                {
                    this.etlRun.setTotalTickets(tickets.size());
                    this.saveEtlRun();
                }
            } else {
                tickets = new HashSet<>();
                log.warn("TicketsRepository is null, initialized empty collection");
            }

            log.info("Loading TicketHistorique...");
            if (ticketHistoriqueRepository != null) {
                ticketsHistorique = new HashSet<>(ticketHistoriqueRepository.getAll());
                log.info("Loaded {} ticket historique records from source", ticketsHistorique.size());
            } else {
                ticketsHistorique = new HashSet<>();
                log.warn("TicketHistoriqueRepository is null, initialized empty collection");
            }

            log.info("Loading Contacts...");
            if (contactsRepository != null) {
                contacts = new HashSet<>(contactsRepository.getAll());
                log.info("Loaded {} contacts from source", contacts.size());
            } else {
                contacts = new HashSet<>();
                log.warn("ContactsRepository is null, initialized empty collection");
            }

            log.info("Loading Commandes...");
            if (commandesRepository != null) {
                commandes = new HashSet<>(commandesRepository.getAll());
                log.info("Loaded {} commandes from source", commandes.size());
            } else {
                commandes = new HashSet<>();
                log.warn("CommandesRepository is null, initialized empty collection");
            }

            log.info("Loading Categories...");
            if (categoriesRepository != null) {
                categories = new HashSet<>(categoriesRepository.getAll());
                log.info("Loaded {} categories from source", categories.size());
            } else {
                categories = new HashSet<>();
                log.warn("CategoriesRepository is null, initialized empty collection");
            }

            int totalSourceEntities = tickets.size() + ticketsHistorique.size() + contacts.size() +
                    commandes.size() + categories.size();
            log.info("Source data loaded successfully - Total entities: {}", totalSourceEntities);
        } catch (Exception e) {
            log.warn("Failed to load source data: {}. Initializing empty collections.", e.getMessage());
            tickets = new HashSet<>();
            ticketsHistorique = new HashSet<>();
            contacts = new HashSet<>();
            commandes = new HashSet<>();
            categories = new HashSet<>();
        }
    }

    private void loadTargetData() {
        try {
            log.info("Starting to load target data...");

            // Initialize abstractListeValeurs by combining all concrete entities that extend AbstractListeValeurs
            abstractListeValeurs = new HashSet<>();

            log.info("Loading TypeDocument...");
            if (typeDocumentRepository != null) {
                typeDocuments = new HashSet<>(typeDocumentRepository.findAll());
                abstractListeValeurs.addAll(typeDocuments);
                log.info("Loaded {} type documents from target", typeDocuments.size());
            } else {
                typeDocuments = new HashSet<>();
                log.warn("TypeDocumentRepository is null, initialized empty collection");
            }

            log.info("Loading TypeMission...");
            if (typeMissionRepository != null) {
                typeMissions = new HashSet<>(typeMissionRepository.findAll());
                abstractListeValeurs.addAll(typeMissions);
                log.info("Loaded {} type missions from target", typeMissions.size());
            } else {
                typeMissions = new HashSet<>();
                log.warn("TypeMissionRepository is null, initialized empty collection");
            }

            log.info("Loading TypePersonne...");
            if (typePersonneRepository != null) {
                typePersonnes = new HashSet<>(typePersonneRepository.findAll());
                abstractListeValeurs.addAll(typePersonnes);
                log.info("Loaded {} type personnes from target", typePersonnes.size());
            } else {
                typePersonnes = new HashSet<>();
                log.warn("TypePersonneRepository is null, initialized empty collection");
            }

            log.info("Loading TypeProjet...");
            if (typeProjetRepository != null) {
                typeProjets = new HashSet<>(typeProjetRepository.findAll());
                abstractListeValeurs.addAll(typeProjets);
                log.info("Loaded {} type projets from target", typeProjets.size());
            } else {
                typeProjets = new HashSet<>();
                log.warn("TypeProjetRepository is null, initialized empty collection");
            }

            log.info("Loading NiveauComplexite...");
            if (niveauComplexiteRepository != null) {
                niveauComplexites = new HashSet<>(niveauComplexiteRepository.findAll());
                abstractListeValeurs.addAll(niveauComplexites);
                log.info("Loaded {} niveau complexites from target", niveauComplexites.size());
            } else {
                niveauComplexites = new HashSet<>();
                log.warn("NiveauComplexiteRepository is null, initialized empty collection");
            }

            log.info("Loading IssueStatut...");
            if (issueStatutRepository != null) {
                issueStatuts = new HashSet<>(issueStatutRepository.findAll());
                abstractListeValeurs.addAll(issueStatuts);
                log.info("Loaded {} issue statuts from target", issueStatuts.size());
            } else {
                issueStatuts = new HashSet<>();
                log.warn("IssueStatutRepository is null, initialized empty collection");
            }

            log.info("Loading IssuePriorite...");
            if (issuePrioriteRepository != null) {
                issuePriorites = new HashSet<>(issuePrioriteRepository.findAll());
                abstractListeValeurs.addAll(issuePriorites);
                log.info("Loaded {} issue priorites from target", issuePriorites.size());
            } else {
                issuePriorites = new HashSet<>();
                log.warn("IssuePrioriteRepository is null, initialized empty collection");
            }

            log.info("Loading IssueOrigine...");
            if (issueOrigineRepository != null) {
                issueOrigines = new HashSet<>(issueOrigineRepository.findAll());
                abstractListeValeurs.addAll(issueOrigines);
                log.info("Loaded {} issue origines from target", issueOrigines.size());
            } else {
                issueOrigines = new HashSet<>();
                log.warn("IssueOrigineRepository is null, initialized empty collection");
            }

            log.info("Total abstract liste valeurs entities: {}", abstractListeValeurs.size());

            // Initialize other entities
            log.info("Loading Commanditaire...");
            if (commanditaireRepository != null) {
                commanditaires = new HashSet<>(commanditaireRepository.findAll());
                log.info("Loaded {} commanditaires from target", commanditaires.size());
            } else {
                commanditaires = new HashSet<>();
                log.warn("CommanditaireRepository is null, initialized empty collection");
            }

            log.info("Loading DocumentContractuel...");
            if (documentContractuelRepository != null) {
                documentContractuels = new HashSet<>(documentContractuelRepository.findAll());
                log.info("Loaded {} document contractuels from target", documentContractuels.size());
            } else {
                documentContractuels = new HashSet<>();
                log.warn("DocumentContractuelRepository is null, initialized empty collection");
            }

            log.info("Loading IssueInformatique...");
            if (issueInformatiqueRepository != null) {
                issueInformatiques = new HashSet<>(issueInformatiqueRepository.findAll());
                log.info("Loaded {} issue informatiques from target", issueInformatiques.size());
            } else {
                issueInformatiques = new HashSet<>();
                log.warn("IssueInformatiqueRepository is null, initialized empty collection");
            }

            log.info("Loading IssuePieceJointe...");
            if (issuePieceJointeRepository != null) {
                issuePieceJointes = new HashSet<>(issuePieceJointeRepository.findAll());
                log.info("Loaded {} issue piece jointes from target", issuePieceJointes.size());
            } else {
                issuePieceJointes = new HashSet<>();
                log.warn("IssuePieceJointeRepository is null, initialized empty collection");
            }

            log.info("Loading IssueRelation...");
            if (issueRelationRepository != null) {
                issueRelations = new HashSet<>(issueRelationRepository.findAll());
                log.info("Loaded {} issue relations from target", issueRelations.size());
            } else {
                issueRelations = new HashSet<>();
                log.warn("IssueRelationRepository is null, initialized empty collection");
            }

            log.info("Loading JournalDetails...");
            if (journalDetailsRepository != null) {
                journalDetails = new HashSet<>(journalDetailsRepository.findAll());
                log.info("Loaded {} journal details from target", journalDetails.size());
            } else {
                journalDetails = new HashSet<>();
                log.warn("JournalDetailsRepository is null, initialized empty collection");
            }

            log.info("Loading Journal...");
            if (journalRepository != null) {
                journaux = new HashSet<>(journalRepository.findAll());
                log.info("Loaded {} journaux from target", journaux.size());
            } else {
                journaux = new HashSet<>();
                log.warn("JournalRepository is null, initialized empty collection");
            }

            log.info("Loading Mission...");
            if (missionRepository != null) {
                missions = new HashSet<>(missionRepository.findAll());
                log.info("Loaded {} missions from target", missions.size());
            } else {
                missions = new HashSet<>();
                log.warn("MissionRepository is null, initialized empty collection");
            }

            log.info("Loading Personne...");
            if (personneRepository != null) {
                personnes = new HashSet<>(personneRepository.findAll());
                log.info("Loaded {} personnes from target", personnes.size());
            } else {
                personnes = new HashSet<>();
                log.warn("PersonneRepository is null, initialized empty collection");
            }

            log.info("Loading Projet...");
            if (projetRepository != null) {
                projets = new HashSet<>(projetRepository.findAll());
                log.info("Loaded {} projets from target", projets.size());
            } else {
                projets = new HashSet<>();
                log.warn("ProjetRepository is null, initialized empty collection");
            }

            log.info("Loading DomaineMetier...");
            if (domaineMetierRepository != null) {
                domaineMetiers = new HashSet<>(domaineMetierRepository.findAll());
                log.info("Loaded {} domaine metiers from target", domaineMetiers.size());
            } else {
                log.warn("DomaineMetierRepository is null, initializing empty collection");
                domaineMetiers = new HashSet<>();
            }

            int totalTargetEntities = commanditaires.size() + documentContractuels.size() +
                    issueInformatiques.size() + issuePieceJointes.size() +
                    issueRelations.size() + journalDetails.size() + journaux.size() +
                    missions.size() + personnes.size() + projets.size() +
                    abstractListeValeurs.size();
            log.info("Target data loaded successfully - Total entities: {}", totalTargetEntities);
        } catch (Exception e) {
            log.warn("Failed to load target data: {}. Initializing empty collections.", e.getMessage());
            initializeEmptyTargetCollections();
        }
    }

    private void initializeEmptyCollections() {
        // V1 collections
        if (tickets == null) tickets = new HashSet<>();
        if (ticketsHistorique == null) ticketsHistorique = new HashSet<>();
        if (contacts == null) contacts = new HashSet<>();
        if (commandes == null) commandes = new HashSet<>();
        if (categories == null) categories = new HashSet<>();
        if (utilisateurs == null) utilisateurs = new HashSet<>();

        // V2 collections
        initializeEmptyTargetCollections();
    }

    private void initializeEmptyTargetCollections() {
        if (abstractListeValeurs == null) abstractListeValeurs = new HashSet<>();
        if (commanditaires == null) commanditaires = new HashSet<>();
        if (documentContractuels == null) documentContractuels = new HashSet<>();
        if (domaineMetiers == null) domaineMetiers = new HashSet<>();
        if (issueInformatiques == null) issueInformatiques = new HashSet<>();
        if (issueOrigines == null) issueOrigines = new HashSet<>();
        if (issuePieceJointes == null) issuePieceJointes = new HashSet<>();
        if (issuePriorites == null) issuePriorites = new HashSet<>();
        if (issueRelations == null) issueRelations = new HashSet<>();
        if (issueStatuts == null) issueStatuts = new HashSet<>();
        if (journalDetails == null) journalDetails = new HashSet<>();
        if (journaux == null) journaux = new HashSet<>();
        if (missions == null) missions = new HashSet<>();
        if (niveauComplexites == null) niveauComplexites = new HashSet<>();
        if (personnes == null) personnes = new HashSet<>();
        if (projets == null) projets = new HashSet<>();
        if (typeDocuments == null) typeDocuments = new HashSet<>();
        if (typeMissions == null) typeMissions = new HashSet<>();
        if (typePersonnes == null) typePersonnes = new HashSet<>();
        if (typeProjets == null) typeProjets = new HashSet<>();
    }

    public EtlRun initEtlRun() {
        try {
            log.info("Initializing new ETL run...");

            EtlRun newEtlRun = new EtlRun();

            // Generate a unique run ID - using timestamp + random component for uniqueness
            String runId = "ETL_" + System.currentTimeMillis() + "_" +
                    String.format("%04d", (int)(Math.random() * 10000));

            newEtlRun.setRunId(runId);
            newEtlRun.setStartTime(LocalDateTime.now());
            newEtlRun.setStatus(EtlRun.EtlRunStatus.STARTED);

            // Initialize counters to prevent null values
            newEtlRun.setProcessedTickets(0);
            newEtlRun.setSuccessfulTickets(0);
            newEtlRun.setFailedTickets(0);
            newEtlRun.setTotalTickets(0);

            // Save to database
            EtlRun savedEtlRun = this.etlRunRepository.save(newEtlRun);

            log.info("ETL run initialized successfully with ID: {}", savedEtlRun.getRunId());
            return savedEtlRun;

        } catch (Exception e) {
            log.error("Failed to initialize ETL run: {}", e.getMessage(), e);
            throw new RuntimeException("Could not initialize ETL run", e);
        }
    }

    public void setEtlRunEnd(boolean IsFailed, String ErrorMessage)
    {
        if(this.etlRun == null)
            return;

        EtlRun.EtlRunStatus status = IsFailed ? EtlRun.EtlRunStatus.FAILED : EtlRun.EtlRunStatus.COMPLETED;
        this.etlRun.setStatus(status);
        this.etlRun.setEndTime(LocalDateTime.now());

        if(ErrorMessage != null && !ErrorMessage.isEmpty())
            this.etlRun.setErrorMessage(ErrorMessage);

        this.saveEtlRun();
    }

    public void saveEtlRun()
    {
        if(this.etlRun == null || this.etlRunRepository == null)
        {
            return;
        }

        this.etlRunRepository.save(this.etlRun);
    }
}
