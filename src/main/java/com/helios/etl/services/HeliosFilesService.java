
package com.helios.etl.services;

import com.helios.etl.config.HeliosPathConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Service
public class HeliosFilesService {
    private static final Logger log = LoggerFactory.getLogger(HeliosFilesService.class);
    private final HeliosPathConfig heliosPathConfig;

    public HeliosFilesService(HeliosPathConfig heliosPathConfig) {
        this.heliosPathConfig = heliosPathConfig;
    }

    public int getCommandesFilesCount() {
        try {
            Path commandesPath = Paths.get(heliosPathConfig.getCommandes());
            log.debug("Checking commandes directory: {}", commandesPath);

            if (!Files.exists(commandesPath) || !Files.isDirectory(commandesPath)) {
                return 0;
            }

            int count = 0;
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(commandesPath)) {
                for (Path entry : stream) {
                    count++;
                }
            }

            return count;
        } catch (IOException e) {
            System.err.println("Error counting files in commandes directory: " + e.getMessage());
            return 0;
        }
    }
}