package com.helios.etl.services.transformers;

import com.helios.etl.helper.DescriptionHelper;
import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.Mission;
import com.helios.etl.model.TypeMission;
import com.helios.etl.services.CacheMemory;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class TypeToMissionTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    public TypeToMissionTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public Mission transform(String pole, String type) {
        Mission mission = TryGetExistingMission(pole, type);
        if(mission != null)
        {
            return mission;
        }

        mission = new Mission();

        TypeMission typemission = _cm.getHelper().tryGetTypeMissionFromPoleAndType(pole, type);
        if(typemission == null)
        {
            typemission = new TypeMission();
            typemission.setLibelle(type);
            typemission.setDescription(DescriptionHelper.GetTicketTypeDescription(type));

            typemission = _cm.getTypeMissionRepository().save(typemission);
            _cm.getTypeMissions().add(typemission);
        }


        String defaultDescriptionMission = "[Description par defaut] " + typemission.getDescription();
        mission.setLibelle(type);
        mission.setDescription(defaultDescriptionMission);
        mission.setType(typemission);
        Set<DomaineMetier> domaines = new HashSet<>(typemission.getDomainesMetier());
        mission.setDomaines(domaines);
        mission.setDomainePrincipal(typemission.getDomainesMetier().stream().findFirst().orElse(null));
        mission.setDateCreation(LocalDateTime.now());
        mission.setDateModification(LocalDateTime.now());

        mission = _cm.getMissionRepository().save(mission);
        _cm.getMissions().add(mission);

        return mission;
    }

    private Mission TryGetExistingMission(String pole, String type)
    {
        for(Mission m : _cm.getMissions())
        {
            if(Objects.equals(m.getType().getLibelle(), type) &&
                (
                    Objects.equals(m.getDomainePrincipal().getLibelle(), pole) ||
                    _cm.getHelper().HasDomaineMetierFromPole(m.getDomaines(), pole)
                ))
            {
                return m;
            }
        }

        return null;
    }
}
