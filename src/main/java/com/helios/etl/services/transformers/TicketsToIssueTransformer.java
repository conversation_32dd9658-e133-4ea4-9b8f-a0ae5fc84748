package com.helios.etl.services.transformers;

import com.helios.etl.model.*;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.services.HeliosFilesService;
import com.helios.etl.source.entities.*;
import com.helios.etl.utils.TransformationResult;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

public class TicketsToIssueTransformer {
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(TicketsToIssueTransformer.class);

    @Getter
    @Setter
    private CacheMemory _cm;

    @PersistenceContext
    private EntityManager _em;

    private TransformationResult transformationResult = new TransformationResult();

    private final HeliosFilesService heliosFilesService;

    public TicketsToIssueTransformer(EntityManager em, HeliosFilesService heliosFilesService) {
        this._em = em;
        this.heliosFilesService = heliosFilesService;
    }


    public TransformationResult TransformTicketToIssueInformatique(Tickets ticket)
    {
        IssueInformatique issue = new IssueInformatique();
        TicketsHistorique lastEdit = _cm.getTicketHistoriqueRepository().getLastHistoriqueFromTicketId(ticket.getIdTickets());
        LocalDateTime last = lastEdit == null ? ticket.getDateCreation() : lastEdit.getDateModification();

        // Set basic issue properties
        issue.setDateCreation(ticket.getDateCreation());
        issue.setDateModification(last);
        issue.setTempsEstimeMinutes(0);
        issue.setTempsEffectifMinutes(ticket.getTempsTotal());
        Map<String, String> options = new HashMap<String, String>();

        if(ticket.getStatus().equals("Résolu"))
        {
            issue.setDateEffectiveFin(ticket.getDateResolution());
        }

        //Pole to domaine metier
        PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);
        DomaineMetier dm = ptdmt.transform(ticket.getPole());
        if(dm == null)
        {
            String msg = "Failed to transform Pole of ticket " + ticket.getIdTickets() + " to DomaineMetier, skipping";
            return this.getTransformationResultAndLog(msg, false);
        }

        //POLE & Type to Mission(Activite)
        if(ticket.getType().trim().equalsIgnoreCase("installation"))
        {
            TypeToProjetTransformer ptpt = new TypeToProjetTransformer(_cm);
            Projet type = ptpt.transform(ticket.getPole(), ticket.getType());
            if(type == null)
            {
                String msg = "Failed to transform Type of ticket " + ticket.getIdTickets() + " to Projet, skipping";
                return this.getTransformationResultAndLog(msg, false);
            }
            type.setDomainePrincipal(dm);
            issue.setActivite(type);
        } else {
            TypeToMissionTransformer ptmt = new TypeToMissionTransformer(_cm);
            Mission type = ptmt.transform(ticket.getPole(), ticket.getType());
            if(type == null)
            {
                String msg = "Failed to transform Type of ticket " + ticket.getIdTickets() + " to Mission, skipping";
                return this.getTransformationResultAndLog(msg, false);
            }
            type.setDomainePrincipal(dm);
            issue.setActivite(type);
        }

        //SEARCH IF PERSONNE EXIST FOR ASSIGNE OR CREATE ONE -> UtilisateurTransformer
        Utilisateurs assigne = _cm.getHelper().tryGetUtilisateurByUsername(ticket.getAssigne());
        if(assigne == null)
        {
            assigne = _cm.getUtilisateursRepository().getByUsername(ticket.getAssigne());
            if(assigne == null)
            {
                String msg = "Failed to find or create Utilisateur for assignee " + ticket.getAssigne() + " in ticket " + ticket.getIdTickets();
                return this.getTransformationResultAndLog(msg, false);
            }
        }
        UtilisateurToPersonneTransformer utpt = new UtilisateurToPersonneTransformer(_cm);
        Personne p = utpt.transform(assigne);
        if(p == null)
        {
            String msg = "Failed to transform Utilisateur " + ticket.getAssigne() + " to Personne for ticket " + ticket.getIdTickets();
            return this.getTransformationResultAndLog(msg, false);
        }
        issue.setIntervenantPrincipal(p);

        //SEARCH IF PERSONNE EXIST FOR DEMANDEUR OR CREATE ONE -> ContactTransformer
        Contact demandeur = _cm.getContactsRepository().getByFullName(ticket.getDemandeur());
        if(demandeur == null)
        {
            String msg = "Failed to find Contact for demandeur " + ticket.getDemandeur() + " in ticket " + ticket.getIdTickets();
            return this.getTransformationResultAndLog(msg, false);
        }
        ContactToPersonneTransformer ctpt = new ContactToPersonneTransformer(_cm);
        Personne demandeurPersonne = ctpt.transform(demandeur);
        if(demandeurPersonne == null)
        {
            String msg = "Failed to transform Contact " + ticket.getDemandeur() + " to Personne for ticket " + ticket.getIdTickets();
            return this.getTransformationResultAndLog(msg, false);
        }
        issue.setDemandeur(demandeurPersonne);

        //SEARCH IF COMMANDITAIRE EXIST FOR CT_NUM OR CREATE ONE -> ClientToCommanditaireTransformer
        ClientToCommanditaireTransformer ctct = new ClientToCommanditaireTransformer(_cm);
        Commanditaire commanditaire = ctct.transform(ticket.getCtNum());
        if(commanditaire == null)
        {
            String msg = "Failed to transform Client " + ticket.getCtNum() + " to Commanditaire for ticket " + ticket.getIdTickets();
            return this.getTransformationResultAndLog(msg, false);
        }
        issue.setCommanditaire(commanditaire);


        //SEARCH IF JOURNAL EXIST FOR ISSUE OR CREATE THEM FROM TICKETSHISTORIQUE -> TicketHistoryToJournalTransformer
        HashSet<TicketsHistorique> historiques = _cm.getTicketHistoriqueRepository().getAllHistoriqueFromTicketId(ticket.getIdTickets());
        if(historiques == null || historiques.isEmpty())
        {
            String msg = "No TicketHistorique found for ticket " + ticket.getIdTickets() + ", skipping Journal creation";
            return this.getTransformationResultAndLog(msg, false);
        }
        TicketsHistoriqueToJournalTransformer thjt = new TicketsHistoriqueToJournalTransformer(_cm);

        issue = _cm.getIssueInformatiqueRepository().save(issue);

        HashSet<Journal> journaux = thjt.transformCollection(historiques, issue);
        if(journaux.size() != historiques.size())
        {
            String msg = "Failed to transform all TicketHistorique for ticket " + ticket.getIdTickets() + " to Journal, skipping";
            return this.getTransformationResultAndLog(msg, false);
        }

        //STANDBY DESIGN - Store to Options
        //TODO IF SET SEARCH IF MISSION FOR CATEGORIE EXIST OR CREATE ONE -> CategorieTransformer
        //IF SET SEARCH IF MISSION FOR CATEGORIE2 EXIST OR CREATE ONE -> CategorieTransformer
        //IF SET SEARCH IF MISSION FOR CATEGORIE3 EXIST OR CREATE ONE -> CategorieTransformer
        issue.setOptions(this.SetOptions(options, ticket));

        //SEARCH IF TICKET IS LINKED TO A COMMANDE, IF SO, SEARCH IF ISSUE DOCUMENT CONTRACTUEL EXIST FOR THIS COMMANDE OR CREATE ONE -> CommandeTransformer
        Commandes cmd = this.getCommande(ticket);
        if(cmd != null)
        {
            CommandeToDocumentContractuelTransformer ctdct = new CommandeToDocumentContractuelTransformer(_cm);
            DocumentContractuel doc = ctdct.transform(cmd);
            if(doc == null)
            {
                String msg = "Failed to transform Commande for ticket " + ticket.getIdTickets() + " to DocumentContractuel, skipping";
                return this.getTransformationResultAndLog(msg, false);
            }
            HashSet<DocumentContractuel> docs = new HashSet<>();
            docs.add(doc);
            issue.setDocumentsContractuels(docs);
            //TODO ADD PIECE JOINTE FROM THE COMMANDE
        }

        // PERSIST
        issue = _cm.getIssueInformatiqueRepository().save(issue);
        _cm.getIssueInformatiques().add(issue);

        if(issue != null && issue.getOid() > 0)
        {
            this.transformationResult = this.getTransformationResultAndLog(
                    "Ticket " + ticket.getIdTickets() + " transformed to IssueInformatique with ID " + issue.getOid(), true);
        }
        else
        {
            String msg = "Failed to transform Ticket " + ticket.getIdTickets() + " to IssueInformatique";
            this.transformationResult = this.getTransformationResultAndLog(msg, false);
        }

        return this.transformationResult;
    }

    private TransformationResult getTransformationResultAndLog(String msg, boolean success) {
        this.transformationResult.setSuccess(success);
        this.transformationResult.setErrorMessage(msg);
        if (success) {
            log.info(msg);
        } else {
            log.warn(msg);
        }
        return transformationResult;
    }

    private Map<String, String> SetOptions(Map<String, String> options, Tickets ticket)
    {
        if(options == null)
        {
            options = new HashMap<>();
        }

        if(ticket.getIdTickets() > 0)
        {
            options.put("helios1.idTicket", String.valueOf(ticket.getIdTickets()));
        }
        if(!ticket.getCategorie().isEmpty())
        {
            options.put("helios1.categorie", ticket.getCategorie());
        }
        if(!ticket.getCategorie2().isEmpty())
        {
            options.put("helios1.categorie2", ticket.getCategorie2());
        }
        if(!ticket.getCategorie3().isEmpty())
        {
            options.put("helios1.categorie3", ticket.getCategorie3());
        }

        return options;
    }

    private DomaineMetier findDomaineMetierByLibelle(String libelle) {
        try {
            return _em.createQuery(
                            "SELECT dm FROM DomaineMetier dm WHERE dm.libelle = :libelle", DomaineMetier.class)
                    .setParameter("libelle", libelle)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null; // No DomaineMetier found with this libelle
        }
    }

    private Commandes getCommande(Tickets ticket)
    {
        for(Commandes commande : _cm.getCommandes())
        {
            if(commande.getIdTickets() == ticket.getIdTickets())
            {
                return commande;
            }
        }

        return null;
    }

}
