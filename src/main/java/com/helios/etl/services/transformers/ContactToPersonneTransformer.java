package com.helios.etl.services.transformers;

import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.Personne;
import com.helios.etl.model.TypePersonne;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Contact;
import com.helios.etl.source.entities.Utilisateurs;

import java.time.LocalDateTime;
import java.util.HashSet;

public class ContactToPersonneTransformer {
    private CacheMemory _cm = null;

    public ContactToPersonneTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public Personne transform(Contact contact) {
        if (!validateContact(contact)) {
            return null;
        }

        Personne personne = tryGetPersonneFromCache(contact);
        if (personne != null) {
            return personne;
        }

        personne = new Personne();
        personne.setNom(contact.getNom().toUpperCase());

        String capitalizedPrenom = contact.getPrenom().substring(0, 1).toUpperCase() + contact.getPrenom().substring(1).toLowerCase();
        personne.setPrenom(capitalizedPrenom);
        personne.setEmail(contact.getEmail());
        personne.setTelephone(contact.getTelephone());
        personne.setFonction(contact.getFonction());
        boolean isInterne = contact.getCtNum().trim().equalsIgnoreCase("AB");

        // Les contacts n'ont pas de DomaineMetier, on utilise un set vide
        HashSet<DomaineMetier> domaineMetiers = new HashSet<>();

        TypePersonne type = tryGetTypePersonneFromCache(contact, isInterne);
        boolean toSaveType = false;
        if(type == null)
        {
            type = new TypePersonne();
            type.setLibelle("Defaut Set");
            type.setInterne(isInterne);

            if(!domaineMetiers.isEmpty()) {
                type.setDomainesMetier(domaineMetiers);
            }
            toSaveType = true;
        }

        if(toSaveType) {
            type = _cm.getTypePersonneRepository().save(type);
            _cm.getTypePersonnes().add(type);
        }

        personne.setType(type);
        personne = _cm.getPersonneRepository().save(personne);
        _cm.getPersonnes().add(personne);

        return personne;
    }

    public HashSet<Personne> transformContactsToPersonnes(HashSet<Contact> contacts) {
        HashSet<Personne> personnes = new HashSet<>();
        for (Contact contact : contacts) {
            Personne personne = this.transform(contact);
            if (personne != null) {
                personnes.add(personne);
            }
        }
        return personnes;
    }

    private TypePersonne tryGetTypePersonneFromCache(Contact contact, boolean isInterne) {
        if (_cm == null || _cm.getTypePersonneRepository() == null) {
            return null;
        }

        for (TypePersonne type : _cm.getTypePersonneRepository().findAll()) {
            if (type.isInterne() == isInterne && type.getLibelle().equalsIgnoreCase("Defaut Set")) {
                return type;
            }
        }

        TypePersonne newType = new TypePersonne();
        newType.setInterne(isInterne);
        newType.setLibelle("Defaut Set");
        newType.setDomainesMetier(new HashSet<DomaineMetier>());
        return newType;
    }

    private boolean validateContact(Contact contact) {
        if (contact == null) {
            return false;
        }
        if (contact.getNom() == null || contact.getNom().isEmpty()) {
            return false;
        }
        if (contact.getPrenom() == null || contact.getPrenom().isEmpty()) {
            return false;
        }

        contact.setNom(contact.getNom().toUpperCase());
        String capitalizedPrenom = contact.getPrenom().substring(0, 1).toUpperCase() + contact.getPrenom().substring(1).toLowerCase();
        contact.setPrenom(capitalizedPrenom);

        if (contact.getEmail() == null) {
            contact.setEmail("");
        }
        if (contact.getTelephone() == null) {
            contact.setTelephone("");
        }
        if (contact.getTelephone2() == null) {
            contact.setTelephone2("");
        }
        if (contact.getTelephone3() == null) {
            contact.setTelephone3("");
        }
        if (contact.getCivilite() == null) {
            contact.setCivilite("");
        }
        if (contact.getCtNo() == null || contact.getCtNo().isEmpty()) {
            contact.setCtNo("N/A");
        }
        if (contact.getCtNum() == null || contact.getCtNum().isEmpty()) {
            contact.setCtNum("N/A");
        }
        if (contact.getDateCreation() == null) {
            contact.setDateCreation(LocalDateTime.now());
        }
        if (contact.getDateModification() == null) {
            contact.setDateModification(LocalDateTime.now());
        }
        if (contact.getFonction() == null)
        {
            contact.setFonction("Inconnue");
        }
        if (contact.getMobile() == null) {
            contact.setMobile("");
        }
        if (contact.getNote() == null) {
            contact.setNote("");
        }
        return true;
    }

    private Personne tryGetPersonneFromCache(Contact c) {
        if (_cm == null || _cm.getPersonnes() == null) {
            return null;
        }

        for (Personne p : _cm.getPersonnes()) {
            if (p.getNom().equalsIgnoreCase(c.getNom()) &&
                    p.getPrenom().equalsIgnoreCase(c.getPrenom())) {
                return p;
            }
        }

        return null;
    }
}
