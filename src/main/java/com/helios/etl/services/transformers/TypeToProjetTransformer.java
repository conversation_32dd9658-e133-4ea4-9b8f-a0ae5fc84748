package com.helios.etl.services.transformers;

import com.helios.etl.helper.DescriptionHelper;
import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.Projet;
import com.helios.etl.model.TypeProjet;
import com.helios.etl.services.CacheMemory;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class TypeToProjetTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    public TypeToProjetTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public Projet transform(String pole, String type) {
        Projet Projet = TryGetExistingProjet(pole, type);
        if(Projet != null)
        {
            return Projet;
        }

        Projet = new Projet();

        TypeProjet typeProjet = _cm.getHelper().tryGetTypeProjetFromPoleAndType(pole, type);
        if(typeProjet == null)
        {
            typeProjet = new TypeProjet();
            typeProjet.setLibelle(type);
            typeProjet.setDescription(DescriptionHelper.GetTicketTypeDescription(type));

            typeProjet = _cm.getTypeProjetRepository().save(typeProjet);
            _cm.getTypeProjets().add(typeProjet);
        }


        String defaultDescriptionProjet = "[Description par defaut] " + typeProjet.getDescription();
        Projet.setLibelle(type);
        Projet.setDescription(defaultDescriptionProjet);
        Projet.setType(typeProjet);
        Set<DomaineMetier> domaines = new HashSet<>(typeProjet.getDomainesMetier());
        Projet.setDomaines(domaines);
        Projet.setDomainePrincipal(typeProjet.getDomainesMetier().stream().findFirst().orElse(null));
        Projet.setDateCreation(LocalDateTime.now());
        Projet.setDateModification(LocalDateTime.now());

        Projet = _cm.getProjetRepository().save(Projet);
        _cm.getProjets().add(Projet);

        return Projet;
    }

    private Projet TryGetExistingProjet(String pole, String type)
    {
        for(Projet m : _cm.getProjets())
        {
            if(Objects.equals(m.getType().getLibelle(), type) &&
                (
                    Objects.equals(m.getDomainePrincipal().getLibelle(), pole) ||
                    _cm.getHelper().HasDomaineMetierFromPole(m.getDomaines(), pole)
                ))
            {
                return m;
            }
        }

        return null;
    }
}
