package com.helios.etl.services.transformers;

import com.helios.etl.model.Commanditaire;
import com.helios.etl.model.Personne;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Contact;

import java.util.HashSet;

public class ClientToCommanditaireTransformer {
    private CacheMemory _cm = null;

    public ClientToCommanditaireTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public Commanditaire transform(String clientName) {
        if (clientName == null || clientName.isEmpty()) {
            return null;
        }

        Commanditaire commanditaire = tryGetCommanditaireFromCache(clientName);
        if (commanditaire != null) {
            // Store transformation mapping for existing commanditaire: Client.name <-> Commanditaire.oid
            if (commanditaire.getOid() > 0) {
                _cm.storeTransformationMapping(
                    "Client",
                    (long) clientName.hashCode(), // Use hashCode as pseudo-ID for string
                    clientName, // Store the actual client name as key
                    "Commanditaire",
                    commanditaire.getOid(),
                    "ClientToCommanditaire",
                    null // metadata
                );
            }
            return commanditaire;
        }

        commanditaire = new Commanditaire();
        commanditaire.setNom(clientName.toUpperCase());

        HashSet<Contact> contacts = _cm.getContactsRepository().getAllByCtNum(clientName);
        if(contacts != null && !contacts.isEmpty()) {
            ContactToPersonneTransformer contactTransformer = new ContactToPersonneTransformer(_cm);
            HashSet<Personne> p = contactTransformer.transformContactsToPersonnes(contacts);
            if(p == null || p.isEmpty())
            {
                return null;
            }
            commanditaire.setPersonnes(p);
        }

        commanditaire = _cm.getCommanditaireRepository().save(commanditaire);
        _cm.getCommanditaires().add(commanditaire);

        // Store transformation mapping: Client.name <-> Commanditaire.oid
        if (commanditaire.getOid() > 0) {
            _cm.storeTransformationMapping(
                "Client",
                (long) clientName.hashCode(), // Use hashCode as pseudo-ID for string
                clientName, // Store the actual client name as key
                "Commanditaire",
                commanditaire.getOid(),
                "ClientToCommanditaire",
                null // metadata
            );
        }

        return commanditaire;
    }

    private Commanditaire tryGetCommanditaireFromCache(String clientName) {
        if (_cm == null || _cm.getCommanditaireRepository() == null) {
            return null;
        }

        Commanditaire c = null;

        for (Commanditaire cmd : _cm.getCommanditaireRepository().findAll()) {
            if (cmd.getNom().equalsIgnoreCase(clientName)) {
                c = cmd;
                break;
            }
        }


        if (c == null)
        {
            c = _cm.getCommanditaireRepository().getByNom(clientName.toUpperCase());
        }

        return c;
    }
}
