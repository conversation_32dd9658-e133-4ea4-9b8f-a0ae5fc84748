package com.helios.etl.services.transformers;

import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.Personne;
import com.helios.etl.model.TypePersonne;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Utilisateurs;

import java.util.HashSet;

public class UtilisateurToPersonneTransformer {
    private CacheMemory _cm = null;

    public UtilisateurToPersonneTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public Personne transform(Utilisateurs utilisateur) {
        if (!validateUtilisateur(utilisateur)) {
            return null;
        }

        Personne personne = tryGetPersonneFromCache(utilisateur);

        if (personne != null) {
            return personne;
        }

        personne = new Personne();
        personne.setNom(utilisateur.getNom().toUpperCase());

        String capitalizedPrenom = utilisateur.getPrenom().substring(0, 1).toUpperCase() + utilisateur.getPrenom().substring(1).toLowerCase();
        personne.setPrenom(capitalizedPrenom);
        personne.setEmail(utilisateur.getEmail());
        personne.setTelephone(utilisateur.getTelephone());

        String defaultFonction = getDefaultFonction(utilisateur);
        personne.setFonction(defaultFonction);

        if (utilisateur.getSociete() == null) {
            utilisateur.setSociete("N/A");
        }

        boolean isInterne = utilisateur.getSociete().trim().equalsIgnoreCase("ACTUELBURO");

        HashSet<DomaineMetier> domaineMetiers = getDomainesMetier(utilisateur);

        TypePersonne type = tryGetTypePersonneFromCache(utilisateur, isInterne);
        boolean toSaveType = false;
        if(type == null)
        {
            type = new TypePersonne();
            type.setLibelle("Defaut Set");
            type.setInterne(isInterne);


            if(!domaineMetiers.isEmpty()) {
                type.setDomainesMetier(domaineMetiers);
            }

            toSaveType = true;
        }

        if(type.getDomainesMetier() == null) {
            type.setDomainesMetier(new HashSet<>());
        }

        if(type.getDomainesMetier().size() != domaineMetiers.size()) {
            type.getDomainesMetier().addAll(domaineMetiers);
            toSaveType = true;
        }

        if(toSaveType) {
            type = _cm.getTypePersonneRepository().save(type);
            if (_cm.getTypePersonnes() != null) {
                _cm.getTypePersonnes().add(type);
            }
        }

        personne.setType(type);
        personne = _cm.getPersonneRepository().save(personne);

        if (_cm.getPersonnes() != null) {
            _cm.getPersonnes().add(personne);
        }

        return personne;
    }


    private TypePersonne tryGetTypePersonneFromCache(Utilisateurs utilisateur, boolean isInterne) {
        if (_cm == null || _cm.getTypePersonnes() == null || utilisateur.getService() == null) {
            return null;
        }

        for (TypePersonne type : _cm.getTypePersonnes()) {
            if (type.getLibelle() != null &&
                    type.getLibelle().equalsIgnoreCase(utilisateur.getService()) &&
                    type.isInterne() == isInterne) {
                return type;
            }
        }

        return null;
    }

    private boolean validateUtilisateur(Utilisateurs utilisateur) {
        return utilisateur != null &&
               utilisateur.getNom() != null && !utilisateur.getNom().trim().isEmpty() &&
               utilisateur.getPrenom() != null && !utilisateur.getPrenom().trim().isEmpty();
    }

    private Personne tryGetPersonneFromCache(Utilisateurs utilisateur) {
        if (_cm == null || _cm.getPersonnes() == null) {
            return null;
        }

        for (Personne p : _cm.getPersonnes()) {
            if (p.getNom().equalsIgnoreCase(utilisateur.getNom()) &&
                p.getPrenom().equalsIgnoreCase(utilisateur.getPrenom())) {
                return p;
            }
        }

        return null;
    }


    private HashSet<DomaineMetier> getDomainesMetier(Utilisateurs utilisateur) {
        HashSet<DomaineMetier> domainesMetiers = new HashSet<>();
        PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);

        // Add null check for getMultiPole()
        if (utilisateur.getMultiPole() != null) {
            String multiPole = utilisateur.getMultiPole().trim().toLowerCase();

            DomaineMetier d = switch (multiPole) {
                case "informatique" -> ptdmt.transform("Informatique");
                case "telecom" -> ptdmt.transform("Telecom");
                case "logiciel" -> ptdmt.transform("Logiciel");
                case "bureautique" -> ptdmt.transform("Bureautique");
                default -> null;
            };

            if (d != null) {
                domainesMetiers.add(d);
            }
        }

        if(domainesMetiers.isEmpty()) {
            DomaineMetier d = ptdmt.transform(utilisateur.getService());
            if (d != null) {
                domainesMetiers.add(d);
            }
        }

        return domainesMetiers;
    }

    private String getDefaultFonction(Utilisateurs u)
    {
        boolean isTerrain = u.getTerrain();
        String defaultFonction = u.getService();
        if(isTerrain)
        {
            defaultFonction += " (Terrain)";
        }

        if(defaultFonction == null || defaultFonction.isEmpty())
        {
            defaultFonction = "Non renseigné";
        }

        return defaultFonction;
    }
}
