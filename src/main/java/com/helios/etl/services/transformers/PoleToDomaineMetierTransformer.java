package com.helios.etl.services.transformers;

import com.helios.etl.helper.DescriptionHelper;
import com.helios.etl.model.DomaineMetier;
import com.helios.etl.services.CacheMemory;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

public class PoleToDomaineMetierTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    public PoleToDomaineMetierTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public DomaineMetier transform(String pole)
    {
        if (pole == null || pole.trim().isEmpty()) {
            return null;
        }

        DomaineMetier dm = null;

        String capitalizedPole = pole.substring(0, 1).toUpperCase() + pole.substring(1).toLowerCase();

        for (DomaineMetier domaine : _cm.getDomaineMetiers()) {
            if (domaine.getLibelle().equalsIgnoreCase(capitalizedPole)) {
                dm = domaine;
                break;
            }
        }

        if(dm == null)
        {
            dm = new DomaineMetier();
            dm.setLibelle(capitalizedPole);
            dm.setDescription(DescriptionHelper.GetPoleDescription(pole));
            dm.setDateCreation(LocalDateTime.now());

            dm = _cm.getDomaineMetierRepository().save(dm);
            _cm.getDomaineMetiers().add(dm);
        }

        return dm;
    }
}
