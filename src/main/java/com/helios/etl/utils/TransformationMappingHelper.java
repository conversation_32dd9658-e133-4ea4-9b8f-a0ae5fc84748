package com.helios.etl.utils;

import com.helios.etl.progress.entities.TransformationMapping;
import com.helios.etl.progress.services.TransformationMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * Helper utility class for working with transformation mappings.
 * Provides convenient methods to query and display transformation equivalents.
 */
@Component
public class TransformationMappingHelper {
    
    @Autowired
    private TransformationMappingService mappingService;
    
    /**
     * Find the target entity OID for a given source entity
     * 
     * @param sourceEntityType Type of source entity (e.g., "Tickets", "Contact")
     * @param sourceEntityId ID of source entity
     * @return Optional containing target OID if mapping exists
     */
    public Optional<Long> findTargetOid(String sourceEntityType, Long sourceEntityId) {
        return mappingService.findTargetOid(sourceEntityType, sourceEntityId);
    }
    
    /**
     * Find the source entity ID for a given target entity
     * 
     * @param targetEntityType Type of target entity (e.g., "IssueInformatique", "Personne")
     * @param targetEntityOid OID of target entity
     * @return Optional containing source ID if mapping exists
     */
    public Optional<Long> findSourceId(String targetEntityType, Long targetEntityOid) {
        return mappingService.findSourceId(targetEntityType, targetEntityOid);
    }
    
    /**
     * Get all mappings for a specific transformation type
     * 
     * @param transformationType Type of transformation (e.g., "TicketsToIssue", "ContactToPersonne")
     * @return List of transformation mappings
     */
    public List<TransformationMapping> getMappingsByType(String transformationType) {
        return mappingService.getMappingsByTransformationType(transformationType);
    }
    
    /**
     * Get all mappings for a specific source entity type
     * 
     * @param sourceEntityType Type of source entity
     * @return List of transformation mappings
     */
    public List<TransformationMapping> getMappingsBySourceType(String sourceEntityType) {
        return mappingService.getMappingsBySourceEntityType(sourceEntityType);
    }
    
    /**
     * Get all mappings for a specific target entity type
     * 
     * @param targetEntityType Type of target entity
     * @return List of transformation mappings
     */
    public List<TransformationMapping> getMappingsByTargetType(String targetEntityType) {
        return mappingService.getMappingsByTargetEntityType(targetEntityType);
    }
    
    /**
     * Print transformation mapping statistics to console
     */
    public void printMappingStatistics() {
        System.out.println("=== Transformation Mapping Statistics ===");
        
        List<Object[]> stats = mappingService.getMappingStatistics();
        System.out.println("Mappings by Transformation Type:");
        for (Object[] stat : stats) {
            String transformationType = (String) stat[0];
            Long count = (Long) stat[1];
            System.out.printf("  %-25s: %d mappings%n", transformationType, count);
        }
        
        long totalMappings = mappingService.getTotalActiveMappings();
        System.out.printf("Total Active Mappings: %d%n", totalMappings);
        System.out.println("==========================================");
    }
    
    /**
     * Print all mappings for a specific transformation type
     * 
     * @param transformationType Type of transformation to display
     */
    public void printMappingsForType(String transformationType) {
        System.out.printf("=== Mappings for %s ===%n", transformationType);
        
        List<TransformationMapping> mappings = getMappingsByType(transformationType);
        if (mappings.isEmpty()) {
            System.out.println("No mappings found for this transformation type.");
        } else {
            System.out.printf("Found %d mappings:%n", mappings.size());
            for (TransformationMapping mapping : mappings) {
                System.out.printf("  %s[%d] -> %s[%d]%n", 
                    mapping.getSourceEntityType(), 
                    mapping.getSourceEntityId(),
                    mapping.getTargetEntityType(), 
                    mapping.getTargetEntityOid());
                
                if (mapping.getSourceEntityKey() != null) {
                    System.out.printf("    Source Key: %s%n", mapping.getSourceEntityKey());
                }
                if (mapping.getMetadata() != null) {
                    System.out.printf("    Metadata: %s%n", mapping.getMetadata());
                }
            }
        }
        System.out.println("==========================================");
    }
    
    /**
     * Find and print the equivalent entity for a given source
     * 
     * @param sourceEntityType Type of source entity
     * @param sourceEntityId ID of source entity
     */
    public void printEquivalentFor(String sourceEntityType, Long sourceEntityId) {
        Optional<Long> targetOid = findTargetOid(sourceEntityType, sourceEntityId);
        
        if (targetOid.isPresent()) {
            // Get the full mapping to show target type
            Optional<TransformationMapping> mapping = mappingService.getMostRecentMapping(sourceEntityType, sourceEntityId);
            if (mapping.isPresent()) {
                TransformationMapping m = mapping.get();
                System.out.printf("Equivalent found: %s[%d] <-> %s[%d] (via %s)%n",
                    m.getSourceEntityType(), m.getSourceEntityId(),
                    m.getTargetEntityType(), m.getTargetEntityOid(),
                    m.getTransformationType());
            } else {
                System.out.printf("Equivalent found: %s[%d] -> OID[%d]%n", 
                    sourceEntityType, sourceEntityId, targetOid.get());
            }
        } else {
            System.out.printf("No equivalent found for %s[%d]%n", sourceEntityType, sourceEntityId);
        }
    }
    
    /**
     * Check if a mapping exists for a source entity
     * 
     * @param sourceEntityType Type of source entity
     * @param sourceEntityId ID of source entity
     * @return true if mapping exists, false otherwise
     */
    public boolean hasMapping(String sourceEntityType, Long sourceEntityId) {
        return mappingService.hasMappingForSource(sourceEntityType, sourceEntityId);
    }
    
    /**
     * Get mapping history for a source entity
     * 
     * @param sourceEntityType Type of source entity
     * @param sourceEntityId ID of source entity
     * @return List of all mappings (including superseded ones) for this source entity
     */
    public List<TransformationMapping> getMappingHistory(String sourceEntityType, Long sourceEntityId) {
        return mappingService.getMappingHistory(sourceEntityType, sourceEntityId);
    }
}
