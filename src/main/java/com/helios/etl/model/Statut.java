/**
 * 
 */
package com.helios.etl.model;

import com.helios.etl.outer.spring.hibernate.PersistentLiteralEnum;

/**
 * <AUTHOR>
 *
 */
public enum Statut implements PersistentLiteralEnum {

	/**
	 * en cours de création. Non visible ou non actif. Sert à préparer la structuration (tickets, ressources, objectifs).
	 */
	BROUILLON("brouillon"),
	
	/**
	 * en phase active : les tickets sont ouverts, les ressources mobilisées, les actions en cours.
	 */
	ACTIF("actif"),
	
	/**
	 * temporairement suspendu (en attente client, dépendance externe, problème bloquant). Peut avoir des tickets en pause
	 */
	EN_ATTENTE("en_attente"),
	
	/**
	 * Tous les livrables sont réalisés, les tickets sont clos. A conserver à des fins d'historique ou d’analyse.
	 */
	TERMINE("termine"),
	
	/**
	 * en lecture seule. Utilisé uniquement à des fins d’audit ou de référence.
	 */
	ARCHIVE("archive"),
	
	/**
	 * abandonné avant réalisation complète. Peut inclure des tickets fermés partiellement ou déplacés.
	 */
	ANNULE("annule");
	
	/**
	 * 
	 */
	private String type = "brouillon";
	
	/**
	 * @param type
	 */
	private Statut(String type) {
		this.type = type;
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.spring.hibernate.PersistentEnum#getId()
	 */
	@Override
	public String getId() {
		return type;
	}
}
