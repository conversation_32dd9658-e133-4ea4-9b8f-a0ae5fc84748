/**
 * 
 */
package com.helios.etl.model;

import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;

/**
 * Un projet, qui est temporaire et orienté livrable, la mission s’inscrit dans la durée et structure les responsabilités 
 * et les flux de tickets dans les activités récurrentes.
 * <AUTHOR>
 */
@Entity
@DiscriminatorValue("Projet")
public class Projet extends AbstractActivite {
	
	/**
	 * catégorie fonctionnelle du projet, c’est-à-dire sa finalité opérationnelle
	 */
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "projet_type_oid", nullable = true)
	@Getter
	@Setter
	protected TypeProjet type;

	/**
	 * niveau d’exécution estimé d’un projet sous forme de pourcentage entier, compris entre 0 (non commencé) et 100 (entièrement terminé).
	 */
	@Column(nullable = false, columnDefinition = "tinyint unsigned default 0")
	@Min(0)
	@Max(100)
	@Getter
	@Setter
	protected byte avancement;
	
}
