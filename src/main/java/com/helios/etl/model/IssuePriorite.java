/**
 * 
 */
package com.helios.etl.model;

import java.util.Collection;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "hls_issue_priorite")
public class IssuePriorite extends AbstractListeValeurs {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4195490659769705230L;

	/**
	 * expression numérique de la priorité
	 */
	@Column(nullable = false, columnDefinition = "tinyint unsigned default 0")
	@Min(0)
	@Max(200)
	@Getter
	@Setter
	protected byte grade = 0;
	
	/**
	 * Lien avec le domaine 
	 */
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name="hls_issue_priorite_has_domaine_metier",
		joinColumns=@JoinColumn(
			name = "issue_priorite_oid", 
			referencedColumnName = "oid"
		), 
		inverseJoinColumns = @JoinColumn(
			name = "domaine_metier_oid",
			referencedColumnName = "oid"
		)
	)
	@Getter
	@Setter
	@JsonIgnore
	protected Collection <DomaineMetier> domainesMetier;

}
