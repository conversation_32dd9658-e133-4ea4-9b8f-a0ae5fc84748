/**
 * 
 */
package com.helios.etl.model;

import java.time.LocalDateTime;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.helios.etl.outer.DateTimeConstants;
import com.helios.etl.outer.model.ExternalEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * Un DocumentContractuel représente un document formel à valeur contractuelle ou justificative, rattaché à une Issue.
 * Il peut s’agir de contrats, factures, bons de commande, devis, conditions générales, ou tout autre document encadrant juridiquement,
 * financièrement ou commercialement l’intervention ou le traitement d’une demande.
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "hls_document_contractuel")
public class DocumentContractuel extends ExternalEntity {

	/**
	 * Nom lisible affiché dans l’interface utilisateur.
	 */
	@Column(length = 200, nullable = false)
	@Getter
	@Setter
	protected String libelle = "";
	
	/**
	 * Texte explicatif plus détaillé, destiné à documenter les finalités, responsabilités et périmètre de cette pièce jointe
	 */
	@Column(nullable = false, columnDefinition = "text")
	@Getter
	@Setter
	protected String description = "";
	
	/**
	 * Date de création du document
	 */
	@Column(nullable = false)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime dateCreation = LocalDateTime.now();
	
	/**
	 * Date de modification du coment
	 */
	@Column(nullable = false)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime dateModification = LocalDateTime.now();
	
	/**
	 * Date de début de validité
	 */
	@Column(nullable = false)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime dateDebutValidite = LocalDateTime.now();
	
	/**
	 * Date de fin de validité
	 */
	@Column(nullable = false)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime dateFinValidite = LocalDateTime.now();
	
}
