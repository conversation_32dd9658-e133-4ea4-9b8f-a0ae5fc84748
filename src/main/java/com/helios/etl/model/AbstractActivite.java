/**
 * 
 */
package com.helios.etl.model;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

import com.helios.etl.outer.spring.jpa.StatutUserType;
import org.hibernate.annotations.Type;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import com.helios.etl.outer.DateTimeConstants;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;

/**
 * Une Activité abstraite représentant tout contexte structurant des tickets ou des actions métiers. 
 * Il peut s'agir d’un projet ponctuel ou d’une mission récurrente, rattaché à un DomaineMetier qui définit le périmètre métier concerné
 * <AUTHOR>
 */

@JsonTypeInfo(
		  use = JsonTypeInfo.Id.NAME,
		  include = JsonTypeInfo.As.EXISTING_PROPERTY,
		  property = "type"
		)
		@JsonSubTypes({
		  @JsonSubTypes.Type(value = Mission.class, name = "Mission"),
		  @JsonSubTypes.Type(value = Projet.class, name = "Projet")
		})


@Entity
@Table(name = "hls_activite")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "__type")
public abstract class AbstractActivite {

	/**
	 * Identifiant unique dans la base de données
	 */
	@Id
	@Column(unique = true, nullable = false)
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Getter
	@Setter
	protected long oid;

	/**
	 * Version technique de l’objet, utilisée pour le contrôle d’accès concurrentiel ou l’optimistic lockin
	 */
	@Getter
	@Setter
	@Version
	@JsonIgnore
	protected Integer __version;

	/**
	 * Code fonctionnel court et unique, utilisé pour les intégrations, exports ou l’identification rapide.
	 */
	@Column(length = 50, unique = true, nullable = false)
	@Getter
	@Setter
	protected String code = "";

	/**
	 * Nom lisible affiché dans l’interface utilisateur.
	 */
	@Column(length = 200, nullable = false)
	@Getter
	@Setter
	protected String libelle = "";
	
	/**
	 * Texte explicatif plus détaillé, destiné à documenter les finalités, responsabilités et périmètre du domain
	 */
	@Column(nullable = false, columnDefinition = "text")
	@Getter
	@Setter
	protected String description = "";
	
    /**
     * Statut de l'activité
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Type(value = StatutUserType.class)
    @Getter
    @Setter
    protected Statut statut = Statut.BROUILLON;
	
	/**
	 * Date de création de l'activité
	 */
	@Column(nullable = false)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime dateCreation = LocalDateTime.now();
	
	/**
	 * Date de modification de l'activité
	 */
	@Column(nullable = false)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime dateModification = LocalDateTime.now();

	/**
	 * Date prévisionnelle de début de l'activité
	 */
	@Column(nullable = true)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime datePrevisionnelleDebut = null;
	
	/**
	 * Date prévisionnelle de fin de l'activité
	 */
	@Column(nullable = true)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime datePrevisionnelleFin = null;
	
	/**
	 * Date effective de début de l'activité
	 */
	@Column(nullable = true)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime dateEffectiveDebut = null;
	
	/**
	 * Date effective de fin de l'activité
	 */
	@Column(nullable = true)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime dateEffectiveFin = null;
	
	/**
	 * Le domaine affecté à cette activité
	 */
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "domaine_principal_oid", nullable = false)
	@Getter
	@Setter
	protected DomaineMetier domainePrincipal;
	
	/**
	 * Les domaines concernés pas cette activité en plus du domaine principal
	 */
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(
	    name = "hls_activite_has_domaine",
	    joinColumns = @JoinColumn(name = "activite_oid", referencedColumnName = "oid"),
	    inverseJoinColumns = @JoinColumn(name = "domaine_oid", referencedColumnName = "oid")
	)
	
	
	/*
	 * Les domaines métiers associés à cette activité, permettant de structurer
	 * les responsabilités et les flux de tickets
	 */
	@Getter
	@Setter
	protected Set<DomaineMetier> domaines = new HashSet<>();
	
	
	/**
	 * 
	 */
	public AbstractActivite() {

	}

}
