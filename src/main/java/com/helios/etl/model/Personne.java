/**
 * 
 */
package com.helios.etl.model;

import com.helios.etl.outer.model.ExternalEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * La classe Personne représente une entité humaine identifiée dans le système, pouvant intervenir 
 * à divers titres dans le traitement ou le suivi d’une Issue, d’une Mission, ou d’un Projet.
 * 
 * <AUTHOR>
 * 
 */
@Entity
@Table(name = "hls_personne")
public class Personne extends ExternalEntity {

	/**
	 * Nom de la personne
	 */
	@Column(length = 200, nullable = false)
	@Getter
	@Setter
	protected String nom = "";

	/**
	 * Prénom de la personne
	 */
	@Column(length = 200, nullable = false)
	@Getter
	@Setter
	protected String prenom = "";

	/**
	 * Fonction de la personne
	 */
	@Column(length = 200, nullable = false)
	@Getter
	@Setter
	protected String fonction = "";

	/**
	 * Email de la personne
	 */
	@Column(length = 200, nullable = false)
	@Getter
	@Setter
	protected String email = "";

	/**
	 * Téléphone de la personne
	 */
	@Column(length = 20, nullable = false)
	@Getter
	@Setter
	protected String telephone = "";
	
	/**
	 * Type de personne
	 */
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "personne_type_oid", nullable = false)
	@Getter
	@Setter
	protected TypePersonne type;
	
}
