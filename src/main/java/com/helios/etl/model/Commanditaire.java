/**
 * 
 */
package com.helios.etl.model;

import java.util.HashSet;
import java.util.Set;

import com.helios.etl.outer.model.ExternalEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * Le commanditaire d’une Issue est la personne morale concernée par le traitement de celle-ci.
 * Il s’agit généralement de l’organisation cliente, partenaire ou interne pour laquelle l’issue est ouverte, suivie et résolue.
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "hls_commanditaire")
public class Commanditaire extends ExternalEntity {

	/**
	 * Nom du commanditaire
	 */
	@Column(length = 200, nullable = false)
	@Getter
	@Setter
	protected String nom = "";

	/**
	 * Email du commanditaire
	 */
	@Column(length = 200, nullable = false)
	@Getter
	@Setter
	protected String email = "";

	/**
	 * Téléphone du commanditaire
	 */
	@Column(length = 20, nullable = false)
	@Getter
	@Setter
	protected String telephone = "";
	
	/**
	 * Liste des personnes associées à ce commanditaire
	 */
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(
	    name = "hls_commanditaire_has_personne",
	    joinColumns = @JoinColumn(name = "commanditaire_oid"),
	    inverseJoinColumns = @JoinColumn(name = "personne_oid")
	)
	@Getter
	@Setter
	protected Set<Personne> personnes = new HashSet<>();
	
}
