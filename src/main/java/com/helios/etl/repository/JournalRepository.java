package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.Journal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface JournalRepository extends JpaRepository<Journal, Long> {

    /**
     * Recherche un(e) Journal par son OID
     */
    Optional<Journal> findOneByOid(long oid);
}