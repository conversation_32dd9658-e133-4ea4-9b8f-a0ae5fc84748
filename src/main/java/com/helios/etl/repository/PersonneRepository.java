package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.Personne;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PersonneRepository extends JpaRepository<Personne, Long> {

    /**
     * Recherche un(e) Personne par son OID
     */
    Optional<Personne> findOneByOid(long oid);
}