package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.IssuePieceJointe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IssuePieceJointeRepository extends JpaRepository<IssuePieceJointe, Long> {

    /**
     * Recherche un(e) IssuePieceJointe par son OID
     */
    Optional<IssuePieceJointe> findOneByOid(long oid);
}