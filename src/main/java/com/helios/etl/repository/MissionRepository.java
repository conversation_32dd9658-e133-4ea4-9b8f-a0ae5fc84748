package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.Mission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MissionRepository extends JpaRepository<Mission, Long> {

    /**
     * Recherche un(e) Mission par son OID
     */
    Optional<Mission> findOneByOid(long oid);
}