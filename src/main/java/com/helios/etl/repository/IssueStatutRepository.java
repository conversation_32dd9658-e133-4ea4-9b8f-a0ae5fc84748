package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.IssueStatut;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IssueStatutRepository extends JpaRepository<IssueStatut, Long> {

    /**
     * Recherche un(e) IssueStatut par son OID
     */
    Optional<IssueStatut> findOneByOid(long oid);
}