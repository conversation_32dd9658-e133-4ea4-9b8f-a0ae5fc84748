package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.IssueRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IssueRelationRepository extends JpaRepository<IssueRelation, Long> {

    /**
     * Recherche un(e) IssueRelation par son OID
     */
    Optional<IssueRelation> findOneByOid(long oid);
}