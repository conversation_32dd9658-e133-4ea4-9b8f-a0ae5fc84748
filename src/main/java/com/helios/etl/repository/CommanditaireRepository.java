package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.Commanditaire;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CommanditaireRepository extends JpaRepository<Commanditaire, Long> {

    /**
     * Recherche un(e) Commanditaire par son OID
     */
    Optional<Commanditaire> findOneByOid(long oid);

    Commanditaire getByNom(String nom);
}