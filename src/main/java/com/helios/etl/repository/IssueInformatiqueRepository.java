package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.IssueInformatique;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IssueInformatiqueRepository extends JpaRepository<IssueInformatique, Long> {

    /**
     * Recherche un(e) IssueInformatique par son OID
     */
    Optional<IssueInformatique> findOneByOid(long oid);
}