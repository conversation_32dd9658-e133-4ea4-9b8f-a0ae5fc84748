package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.JournalDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface JournalDetailsRepository extends JpaRepository<JournalDetails, Long> {

    /**
     * Recherche un(e) JournalDetails par son OID
     */
    Optional<JournalDetails> findOneByOid(long oid);
}