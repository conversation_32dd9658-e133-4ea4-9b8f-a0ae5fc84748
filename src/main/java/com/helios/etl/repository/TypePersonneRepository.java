package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.TypePersonne;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TypePersonneRepository extends JpaRepository<TypePersonne, Long> {

    /**
     * Recherche un(e) TypePersonne par son OID
     */
    Optional<TypePersonne> findOneByOid(long oid);
}