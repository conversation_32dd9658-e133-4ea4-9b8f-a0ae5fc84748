package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.TypeProjet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TypeProjetRepository extends JpaRepository<TypeProjet, Long> {

    /**
     * Recherche un(e) TypeProjet par son OID
     */
    Optional<TypeProjet> findOneByOid(long oid);
}