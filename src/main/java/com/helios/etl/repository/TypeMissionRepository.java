package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.TypeMission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TypeMissionRepository extends JpaRepository<TypeMission, Long> {

    /**
     * Recherche un(e) TypeMission par son OID
     */
    Optional<TypeMission> findOneByOid(long oid);
}