package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.NiveauComplexite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface NiveauComplexiteRepository extends JpaRepository<NiveauComplexite, Long> {

    /**
     * Recherche un(e) NiveauComplexite par son OID
     */
    Optional<NiveauComplexite> findOneByOid(long oid);
}