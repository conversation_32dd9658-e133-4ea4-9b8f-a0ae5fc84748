package com.helios.etl.progress.repositories;

import com.helios.etl.progress.entities.TransformationMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TransformationMappingRepository extends JpaRepository<TransformationMapping, Long> {
    
    /**
     * Find active mapping by source entity
     */
    Optional<TransformationMapping> findBySourceEntityTypeAndSourceEntityIdAndStatus(
            String sourceEntityType, Long sourceEntityId, TransformationMapping.MappingStatus status);
    
    /**
     * Find active mapping by source entity with key
     */
    Optional<TransformationMapping> findBySourceEntityTypeAndSourceEntityIdAndSourceEntityKeyAndStatus(
            String sourceEntityType, Long sourceEntityId, String sourceEntityKey, TransformationMapping.MappingStatus status);
    
    /**
     * Find active mapping by target entity
     */
    Optional<TransformationMapping> findByTargetEntityTypeAndTargetEntityOidAndStatus(
            String targetEntityType, Long targetEntityOid, TransformationMapping.MappingStatus status);
    
    /**
     * Find all mappings for a specific transformation type
     */
    List<TransformationMapping> findByTransformationTypeAndStatusOrderByCreatedAtDesc(
            String transformationType, TransformationMapping.MappingStatus status);
    
    /**
     * Find all mappings for a specific ETL run
     */
    List<TransformationMapping> findByEtlRunIdOrderByCreatedAtDesc(Long etlRunId);
    
    /**
     * Find all mappings for a specific source entity type
     */
    List<TransformationMapping> findBySourceEntityTypeAndStatusOrderByCreatedAtDesc(
            String sourceEntityType, TransformationMapping.MappingStatus status);
    
    /**
     * Find all mappings for a specific target entity type
     */
    List<TransformationMapping> findByTargetEntityTypeAndStatusOrderByCreatedAtDesc(
            String targetEntityType, TransformationMapping.MappingStatus status);
    
    /**
     * Check if a mapping exists for a source entity
     */
    boolean existsBySourceEntityTypeAndSourceEntityIdAndStatus(
            String sourceEntityType, Long sourceEntityId, TransformationMapping.MappingStatus status);
    
    /**
     * Check if a mapping exists for a target entity
     */
    boolean existsByTargetEntityTypeAndTargetEntityOidAndStatus(
            String targetEntityType, Long targetEntityOid, TransformationMapping.MappingStatus status);
    
    /**
     * Get mapping statistics by transformation type
     */
    @Query("SELECT tm.transformationType, COUNT(tm) FROM TransformationMapping tm WHERE tm.status = :status GROUP BY tm.transformationType ORDER BY COUNT(tm) DESC")
    List<Object[]> getMappingStatisticsByTransformationType(@Param("status") TransformationMapping.MappingStatus status);
    
    /**
     * Count total active mappings
     */
    long countByStatus(TransformationMapping.MappingStatus status);
    
    /**
     * Count mappings for a specific ETL run
     */
    long countByEtlRunId(Long etlRunId);
    
    /**
     * Find the most recent mapping for a source entity (including superseded ones)
     */
    @Query("SELECT tm FROM TransformationMapping tm WHERE tm.sourceEntityType = :sourceEntityType AND " +
           "tm.sourceEntityId = :sourceEntityId ORDER BY tm.updatedAt DESC LIMIT 1")
    Optional<TransformationMapping> findMostRecentMappingForSource(@Param("sourceEntityType") String sourceEntityType, 
                                                                  @Param("sourceEntityId") Long sourceEntityId);
    
    /**
     * Find all mappings for a source entity (including history)
     */
    List<TransformationMapping> findBySourceEntityTypeAndSourceEntityIdOrderByUpdatedAtDesc(
            String sourceEntityType, Long sourceEntityId);
}
