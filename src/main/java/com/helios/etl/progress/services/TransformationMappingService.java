package com.helios.etl.progress.services;

import com.helios.etl.progress.entities.EtlRun;
import com.helios.etl.progress.entities.TransformationMapping;
import com.helios.etl.progress.repositories.TransformationMappingRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing transformation mappings between source and target entities.
 * This service provides methods to store, retrieve, and query transformation equivalents.
 */
@Service
@Transactional
public class TransformationMappingService {
    
    private static final Logger log = LoggerFactory.getLogger(TransformationMappingService.class);
    
    @Autowired
    private TransformationMappingRepository mappingRepository;
    
    /**
     * Create or update a transformation mapping
     */
    public TransformationMapping createMapping(String sourceEntityType, Long sourceEntityId,
                                             String targetEntityType, Long targetEntityOid,
                                             String transformationType, EtlRun etlRun) {
        return createMapping(sourceEntityType, sourceEntityId, null, targetEntityType, 
                           targetEntityOid, transformationType, etlRun, null);
    }
    
    /**
     * Create or update a transformation mapping with additional context
     */
    public TransformationMapping createMapping(String sourceEntityType, Long sourceEntityId, String sourceEntityKey,
                                             String targetEntityType, Long targetEntityOid,
                                             String transformationType, EtlRun etlRun, String metadata) {
        
        // Check if mapping already exists
        Optional<TransformationMapping> existingMapping = sourceEntityKey != null ?
            mappingRepository.findBySourceEntityTypeAndSourceEntityIdAndSourceEntityKeyAndStatus(
                sourceEntityType, sourceEntityId, sourceEntityKey, TransformationMapping.MappingStatus.ACTIVE) :
            mappingRepository.findBySourceEntityTypeAndSourceEntityIdAndStatus(
                sourceEntityType, sourceEntityId, TransformationMapping.MappingStatus.ACTIVE);
        
        if (existingMapping.isPresent()) {
            TransformationMapping existing = existingMapping.get();
            
            // If target is the same, just update timestamp
            if (existing.getTargetEntityOid().equals(targetEntityOid) && 
                existing.getTargetEntityType().equals(targetEntityType)) {
                existing.updateTimestamp();
                if (metadata != null) {
                    existing.setMetadata(metadata);
                }
                log.debug("Updated existing mapping: {}", existing.getDescription());
                return mappingRepository.save(existing);
            }
            
            // If target is different, supersede the old mapping and create new one
            existing.markAsSuperseded();
            mappingRepository.save(existing);
            log.info("Superseded existing mapping: {}", existing.getDescription());
        }
        
        // Create new mapping
        TransformationMapping newMapping = new TransformationMapping(
            sourceEntityType, sourceEntityId, sourceEntityKey,
            targetEntityType, targetEntityOid, transformationType, etlRun);
        
        if (metadata != null) {
            newMapping.setMetadata(metadata);
        }
        
        TransformationMapping saved = mappingRepository.save(newMapping);
        log.info("Created new transformation mapping: {}", saved.getDescription());
        
        return saved;
    }
    
    /**
     * Find target entity OID by source entity
     */
    public Optional<Long> findTargetOid(String sourceEntityType, Long sourceEntityId) {
        return mappingRepository.findBySourceEntityTypeAndSourceEntityIdAndStatus(
                sourceEntityType, sourceEntityId, TransformationMapping.MappingStatus.ACTIVE)
                .map(TransformationMapping::getTargetEntityOid);
    }
    
    /**
     * Find target entity OID by source entity with key
     */
    public Optional<Long> findTargetOid(String sourceEntityType, Long sourceEntityId, String sourceEntityKey) {
        return mappingRepository.findBySourceEntityTypeAndSourceEntityIdAndSourceEntityKeyAndStatus(
                sourceEntityType, sourceEntityId, sourceEntityKey, TransformationMapping.MappingStatus.ACTIVE)
                .map(TransformationMapping::getTargetEntityOid);
    }
    
    /**
     * Find source entity ID by target entity
     */
    public Optional<Long> findSourceId(String targetEntityType, Long targetEntityOid) {
        return mappingRepository.findByTargetEntityTypeAndTargetEntityOidAndStatus(
                targetEntityType, targetEntityOid, TransformationMapping.MappingStatus.ACTIVE)
                .map(TransformationMapping::getSourceEntityId);
    }
    
    /**
     * Check if a mapping exists for a source entity
     */
    public boolean hasMappingForSource(String sourceEntityType, Long sourceEntityId) {
        return mappingRepository.existsBySourceEntityTypeAndSourceEntityIdAndStatus(
                sourceEntityType, sourceEntityId, TransformationMapping.MappingStatus.ACTIVE);
    }
    
    /**
     * Check if a mapping exists for a target entity
     */
    public boolean hasMappingForTarget(String targetEntityType, Long targetEntityOid) {
        return mappingRepository.existsByTargetEntityTypeAndTargetEntityOidAndStatus(
                targetEntityType, targetEntityOid, TransformationMapping.MappingStatus.ACTIVE);
    }
    
    /**
     * Get all mappings for a specific transformation type
     */
    public List<TransformationMapping> getMappingsByTransformationType(String transformationType) {
        return mappingRepository.findByTransformationTypeAndStatusOrderByCreatedAtDesc(
                transformationType, TransformationMapping.MappingStatus.ACTIVE);
    }
    
    /**
     * Get all mappings for a specific ETL run
     */
    public List<TransformationMapping> getMappingsByEtlRun(Long etlRunId) {
        return mappingRepository.findByEtlRunIdOrderByCreatedAtDesc(etlRunId);
    }
    
    /**
     * Get all mappings for a specific source entity type
     */
    public List<TransformationMapping> getMappingsBySourceEntityType(String sourceEntityType) {
        return mappingRepository.findBySourceEntityTypeAndStatusOrderByCreatedAtDesc(
                sourceEntityType, TransformationMapping.MappingStatus.ACTIVE);
    }
    
    /**
     * Get all mappings for a specific target entity type
     */
    public List<TransformationMapping> getMappingsByTargetEntityType(String targetEntityType) {
        return mappingRepository.findByTargetEntityTypeAndStatusOrderByCreatedAtDesc(
                targetEntityType, TransformationMapping.MappingStatus.ACTIVE);
    }
    
    /**
     * Get mapping statistics by transformation type
     */
    public List<Object[]> getMappingStatistics() {
        return mappingRepository.getMappingStatisticsByTransformationType(TransformationMapping.MappingStatus.ACTIVE);
    }
    
    /**
     * Get total count of active mappings
     */
    public long getTotalActiveMappings() {
        return mappingRepository.countByStatus(TransformationMapping.MappingStatus.ACTIVE);
    }
    
    /**
     * Get count of mappings for a specific ETL run
     */
    public long getMappingCountForEtlRun(Long etlRunId) {
        return mappingRepository.countByEtlRunId(etlRunId);
    }
    
    /**
     * Delete a mapping (mark as deleted)
     */
    public void deleteMapping(String sourceEntityType, Long sourceEntityId) {
        Optional<TransformationMapping> mapping = mappingRepository.findBySourceEntityTypeAndSourceEntityIdAndStatus(
                sourceEntityType, sourceEntityId, TransformationMapping.MappingStatus.ACTIVE);
        
        if (mapping.isPresent()) {
            TransformationMapping m = mapping.get();
            m.markAsDeleted();
            mappingRepository.save(m);
            log.info("Deleted mapping: {}", m.getDescription());
        }
    }
    
    /**
     * Get mapping history for a source entity
     */
    public List<TransformationMapping> getMappingHistory(String sourceEntityType, Long sourceEntityId) {
        return mappingRepository.findBySourceEntityTypeAndSourceEntityIdOrderByUpdatedAtDesc(
                sourceEntityType, sourceEntityId);
    }
    
    /**
     * Get the most recent mapping for a source entity (including superseded ones)
     */
    public Optional<TransformationMapping> getMostRecentMapping(String sourceEntityType, Long sourceEntityId) {
        return mappingRepository.findMostRecentMappingForSource(sourceEntityType, sourceEntityId);
    }
}
