package com.helios.etl.progress.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Represents the mapping between source and target entities during transformation.
 * This table tracks the equivalents between source IDs and target OIDs for all transformations.
 * 
 * Examples:
 * - Tickets.idTickets <-> AbstractIssue.oid
 * - Contact.idContact <-> Personne.oid
 * - Utilisateurs.idUtilisateur <-> Personne.oid
 */
@Entity
@Table(name = "transformation_mappings", 
       indexes = {
           @Index(name = "idx_source_entity", columnList = "source_entity_type, source_entity_id"),
           @Index(name = "idx_target_entity", columnList = "target_entity_type, target_entity_oid"),
           @Index(name = "idx_etl_run", columnList = "etl_run_id"),
           @Index(name = "idx_transformation_type", columnList = "transformation_type")
       })
@Getter
@Setter
public class TransformationMapping {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * Reference to the ETL run that created this mapping
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "etl_run_id", nullable = true)
    private EtlRun etlRun;
    
    /**
     * Type of the source entity (e.g., "Tickets", "Contact", "Utilisateurs")
     */
    @Column(name = "source_entity_type", length = 100, nullable = false)
    private String sourceEntityType;
    
    /**
     * ID of the source entity (e.g., idTickets, idContact, idUtilisateur)
     */
    @Column(name = "source_entity_id", nullable = false)
    private Long sourceEntityId;
    
    /**
     * Additional source identifier (for composite keys or additional context)
     */
    @Column(name = "source_entity_key", length = 255, nullable = true)
    private String sourceEntityKey;
    
    /**
     * Type of the target entity (e.g., "IssueInformatique", "Personne", "DomaineMetier")
     */
    @Column(name = "target_entity_type", length = 100, nullable = false)
    private String targetEntityType;
    
    /**
     * OID of the target entity (the generated ID in the target system)
     */
    @Column(name = "target_entity_oid", nullable = false)
    private Long targetEntityOid;
    
    /**
     * Type of transformation performed (e.g., "ContactToPersonne", "TicketsToIssue")
     */
    @Column(name = "transformation_type", length = 100, nullable = false)
    private String transformationType;
    
    /**
     * When this mapping was created
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * When this mapping was last updated
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * Additional metadata about the transformation (JSON format)
     */
    @Column(name = "metadata", columnDefinition = "TEXT", nullable = true)
    private String metadata;
    
    /**
     * Status of this mapping (ACTIVE, SUPERSEDED, DELETED)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private MappingStatus status = MappingStatus.ACTIVE;
    
    public enum MappingStatus {
        ACTIVE,     // Currently valid mapping
        SUPERSEDED, // Replaced by a newer mapping
        DELETED     // Logically deleted
    }
    
    // Constructors
    public TransformationMapping() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public TransformationMapping(String sourceEntityType, Long sourceEntityId, 
                               String targetEntityType, Long targetEntityOid, 
                               String transformationType) {
        this();
        this.sourceEntityType = sourceEntityType;
        this.sourceEntityId = sourceEntityId;
        this.targetEntityType = targetEntityType;
        this.targetEntityOid = targetEntityOid;
        this.transformationType = transformationType;
    }
    
    public TransformationMapping(String sourceEntityType, Long sourceEntityId, String sourceEntityKey,
                               String targetEntityType, Long targetEntityOid, 
                               String transformationType, EtlRun etlRun) {
        this(sourceEntityType, sourceEntityId, targetEntityType, targetEntityOid, transformationType);
        this.sourceEntityKey = sourceEntityKey;
        this.etlRun = etlRun;
    }
    
    // Convenience methods
    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
    }
    
    public void markAsSuperseded() {
        this.status = MappingStatus.SUPERSEDED;
        updateTimestamp();
    }
    
    public void markAsDeleted() {
        this.status = MappingStatus.DELETED;
        updateTimestamp();
    }
    
    /**
     * Get a human-readable description of this mapping
     */
    public String getDescription() {
        return String.format("%s[%d] -> %s[%d] via %s", 
                           sourceEntityType, sourceEntityId, 
                           targetEntityType, targetEntityOid, 
                           transformationType);
    }
    
    @Override
    public String toString() {
        return String.format("TransformationMapping{id=%d, %s}", id, getDescription());
    }
}
