# Transformation Mappings

This document explains the transformation mapping system that tracks equivalents between source and target entities during ETL transformations.

## Overview

The transformation mapping system provides a way to track the relationships between source entities (from the legacy system) and target entities (in the new system). This is essential for:

- **Data Traceability**: Know which source record created which target record
- **Incremental Updates**: Update only changed records in subsequent ETL runs
- **Data Validation**: Verify transformation correctness
- **Debugging**: Troubleshoot transformation issues
- **Reporting**: Generate transformation statistics and reports

## Database Schema

The system uses a dedicated table `transformation_mappings` in the progress database (SQLite) with the following structure:

```sql
CREATE TABLE transformation_mappings (
    id BIGINT PRIMARY KEY,
    etl_run_id BIGINT,
    source_entity_type VARCHAR(100) NOT NULL,
    source_entity_id BIGINT NOT NULL,
    source_entity_key VARCHAR(255),
    target_entity_type VARCHAR(100) NOT NULL,
    target_entity_oid BIGINT NOT NULL,
    transformation_type VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    metadata TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE'
);
```

## Key Concepts

### Entity Types
- **Source Entity Type**: The type of the source entity (e.g., "Tickets", "Contact", "Utilisateurs")
- **Target Entity Type**: The type of the target entity (e.g., "IssueInformatique", "Personne", "DomaineMetier")

### Transformation Types
- **TicketsToIssue**: Tickets → IssueInformatique
- **ContactToPersonne**: Contact → Personne
- **UtilisateurToPersonne**: Utilisateurs → Personne
- **ClientToCommanditaire**: Client → Commanditaire
- **PoleToDomaineMetier**: Pole → DomaineMetier

### Mapping Status
- **ACTIVE**: Currently valid mapping
- **SUPERSEDED**: Replaced by a newer mapping
- **DELETED**: Logically deleted

## Usage Examples

### 1. Basic Mapping Examples

```java
// Example 1: Tickets.idTickets <-> IssueInformatique.oid
Tickets ticket = getTicket(12345);
IssueInformatique issue = transformTicketToIssue(ticket);

// Store the mapping
cacheMemory.storeTransformationMapping(
    "Tickets", 
    (long) ticket.getIdTickets(), 
    "IssueInformatique", 
    issue.getOid(), 
    "TicketsToIssue"
);

// Example 2: Contact.idContact <-> Personne.oid
Contact contact = getContact(111);
Personne personne = transformContactToPersonne(contact);

cacheMemory.storeTransformationMapping(
    "Contact", 
    (long) contact.getId(), 
    "Personne", 
    personne.getOid(), 
    "ContactToPersonne"
);
```

### 2. Finding Equivalents

```java
// Find target OID by source entity
Optional<Long> issueOid = cacheMemory.findTargetOid("Tickets", 12345L);
if (issueOid.isPresent()) {
    System.out.println("Ticket 12345 maps to IssueInformatique OID: " + issueOid.get());
}

// Find source ID by target entity
Optional<Long> ticketId = cacheMemory.findSourceId("IssueInformatique", 67890L);
if (ticketId.isPresent()) {
    System.out.println("IssueInformatique 67890 was created from Ticket: " + ticketId.get());
}
```

### 3. Using the Helper Utility

```java
@Autowired
private TransformationMappingHelper mappingHelper;

// Check if mapping exists
boolean hasMapping = mappingHelper.hasMapping("Tickets", 12345L);

// Print statistics
mappingHelper.printMappingStatistics();

// Print mappings for a specific type
mappingHelper.printMappingsForType("TicketsToIssue");

// Find and print equivalent
mappingHelper.printEquivalentFor("Tickets", 12345L);
```

### 4. Querying Mappings

```java
@Autowired
private TransformationMappingService mappingService;

// Get all mappings for a transformation type
List<TransformationMapping> ticketMappings = 
    mappingService.getMappingsByTransformationType("TicketsToIssue");

// Get mapping statistics
List<Object[]> stats = mappingService.getMappingStatistics();
for (Object[] stat : stats) {
    String type = (String) stat[0];
    Long count = (Long) stat[1];
    System.out.println(type + ": " + count + " mappings");
}

// Get total count
long totalMappings = mappingService.getTotalActiveMappings();
```

## Integration with Transformers

The mapping system is automatically integrated into all transformers:

### ContactToPersonneTransformer
```java
// Automatically stores mapping when creating or finding Personne
Personne personne = transform(contact);
// Mapping: Contact.id <-> Personne.oid is stored automatically
```

### UtilisateurToPersonneTransformer
```java
// Automatically stores mapping when creating or finding Personne
Personne personne = transform(utilisateur);
// Mapping: Utilisateurs.idUtilisateur <-> Personne.oid is stored automatically
```

### TicketsToIssueTransformer
```java
// Automatically stores mapping when creating IssueInformatique
IssueInformatique issue = TransformTicketToIssueInformatique(ticket);
// Mapping: Tickets.idTickets <-> IssueInformatique.oid is stored automatically
```

## Special Cases

### String-based Mappings
For transformations that work with string values (like pole names, client names), we use the string's hashCode as a pseudo-ID:

```java
// Client name to Commanditaire
String clientName = "ACME Corp";
cacheMemory.storeTransformationMapping(
    "Client", 
    (long) clientName.hashCode(), // Use hashCode as pseudo-ID
    clientName, // Store actual name as key
    "Commanditaire", 
    commanditaire.getOid(), 
    "ClientToCommanditaire"
);
```

## Benefits

1. **Complete Traceability**: Every transformation is tracked
2. **Data Integrity**: Verify that transformations are working correctly
3. **Performance**: Avoid re-transforming already processed entities
4. **Debugging**: Quickly identify which source record caused issues
5. **Reporting**: Generate comprehensive transformation reports
6. **Incremental Processing**: Support for delta/incremental ETL runs

## Database Location

The transformation mappings are stored in the progress database:
- **File**: `./etl_progress.db` (SQLite)
- **Table**: `transformation_mappings`
- **Configuration**: `progress.datasource.*` properties

## Testing

Run the transformation mapping tests to verify functionality:

```bash
mvn test -Dtest=TransformationMappingTest
```

The test demonstrates:
- Basic mapping creation and retrieval
- Mapping superseding (when same source maps to different targets)
- Statistics and reporting
- Helper utility methods

## Monitoring

Use the helper methods to monitor transformation progress:

```java
// Print current statistics
mappingHelper.printMappingStatistics();

// Check specific mappings
mappingHelper.printMappingsForType("TicketsToIssue");

// Verify specific transformations
mappingHelper.printEquivalentFor("Tickets", 12345L);
```

This system provides complete visibility into the ETL transformation process and ensures data integrity throughout the migration.
