# Transformation Mapping System - Demo & Usage

This document demonstrates how to use the transformation mapping system that tracks equivalents between source and target entities.

## System Overview

The transformation mapping system automatically tracks relationships between:
- **Tickets.idTickets** ↔ **IssueInformatique.oid**
- **Contact.idContact** ↔ **Personne.oid**
- **Utilisateurs.idUtilisateur** ↔ **Personne.oid**
- **Client.name** ↔ **Commanditaire.oid**
- **Pole.name** ↔ **DomaineMetier.oid**

## Database Table

The mappings are stored in the `transformation_mappings` table in the SQLite progress database (`./etl_progress.db`):

```sql
-- View all transformation mappings
SELECT 
    source_entity_type,
    source_entity_id,
    source_entity_key,
    target_entity_type,
    target_entity_oid,
    transformation_type,
    created_at,
    status
FROM transformation_mappings 
WHERE status = 'ACTIVE'
ORDER BY created_at DESC;

-- Find specific mapping: Tickets -> IssueInformatique
SELECT target_entity_oid 
FROM transformation_mappings 
WHERE source_entity_type = 'Tickets' 
  AND source_entity_id = 12345 
  AND status = 'ACTIVE';

-- Get mapping statistics
SELECT 
    transformation_type, 
    COUNT(*) as mapping_count 
FROM transformation_mappings 
WHERE status = 'ACTIVE' 
GROUP BY transformation_type 
ORDER BY mapping_count DESC;
```

## Automatic Integration

The system is automatically integrated into all transformers:

### 1. ContactToPersonneTransformer
```java
// When transforming a Contact to Personne, the mapping is automatically stored
Contact contact = getContact(111);
Personne personne = contactTransformer.transform(contact);
// Mapping stored: Contact[111] -> Personne[personne.getOid()]
```

### 2. UtilisateurToPersonneTransformer
```java
// When transforming a Utilisateur to Personne, the mapping is automatically stored
Utilisateurs utilisateur = getUtilisateur(222);
Personne personne = utilisateurTransformer.transform(utilisateur);
// Mapping stored: Utilisateurs[222] -> Personne[personne.getOid()]
```

### 3. TicketsToIssueTransformer
```java
// When transforming a Ticket to IssueInformatique, the mapping is automatically stored
Tickets ticket = getTicket(12345);
IssueInformatique issue = ticketTransformer.TransformTicketToIssueInformatique(ticket);
// Mapping stored: Tickets[12345] -> IssueInformatique[issue.getOid()]
```

## Manual Usage via CacheMemory

You can also manually store and query mappings using the CacheMemory utility methods:

### Store Mappings
```java
@Autowired
private CacheMemory cacheMemory;

// Store a basic mapping
cacheMemory.storeTransformationMapping(
    "Tickets",           // source entity type
    12345L,              // source entity ID
    "IssueInformatique", // target entity type
    67890L,              // target entity OID
    "TicketsToIssue"     // transformation type
);

// Store a mapping with additional context (for string-based sources)
cacheMemory.storeTransformationMapping(
    "Client",            // source entity type
    "ACME Corp".hashCode(), // pseudo-ID for string
    "ACME Corp",         // actual string as key
    "Commanditaire",     // target entity type
    333L,                // target entity OID
    "ClientToCommanditaire", // transformation type
    null                 // metadata (optional)
);
```

### Query Mappings
```java
// Find target OID by source entity
Optional<Long> issueOid = cacheMemory.findTargetOid("Tickets", 12345L);
if (issueOid.isPresent()) {
    System.out.println("Ticket 12345 maps to IssueInformatique OID: " + issueOid.get());
}

// Find source ID by target entity
Optional<Long> ticketId = cacheMemory.findSourceId("IssueInformatique", 67890L);
if (ticketId.isPresent()) {
    System.out.println("IssueInformatique 67890 was created from Ticket: " + ticketId.get());
}

// Check if mapping exists
boolean hasMapping = cacheMemory.hasTransformationMapping("Tickets", 12345L);

// Get all mappings for current ETL run
List<TransformationMapping> currentMappings = cacheMemory.getCurrentEtlRunMappings();

// Get transformation statistics
List<Object[]> stats = cacheMemory.getTransformationMappingStatistics();
for (Object[] stat : stats) {
    String type = (String) stat[0];
    Long count = (Long) stat[1];
    System.out.println(type + ": " + count + " mappings");
}
```

## Using the Helper Utility

For more advanced operations, use the `TransformationMappingHelper`:

```java
@Autowired
private TransformationMappingHelper mappingHelper;

// Print comprehensive statistics
mappingHelper.printMappingStatistics();

// Print all mappings for a specific transformation type
mappingHelper.printMappingsForType("TicketsToIssue");

// Find and print equivalent for a specific source
mappingHelper.printEquivalentFor("Tickets", 12345L);

// Get mapping history (including superseded mappings)
List<TransformationMapping> history = mappingHelper.getMappingHistory("Tickets", 12345L);
```

## Example Output

When you run the ETL process, you'll see mappings being created automatically:

```
2025-08-21 14:30:15 INFO  TransformationMappingService - Created new transformation mapping: Tickets[12345] -> IssueInformatique[67890] via TicketsToIssue
2025-08-21 14:30:16 INFO  TransformationMappingService - Created new transformation mapping: Contact[111] -> Personne[222] via ContactToPersonne
2025-08-21 14:30:17 INFO  TransformationMappingService - Updated existing mapping: Utilisateurs[333] -> Personne[444] via UtilisateurToPersonne
```

## Querying Statistics

Use the helper to get comprehensive statistics:

```java
mappingHelper.printMappingStatistics();
```

Output:
```
=== Transformation Mapping Statistics ===
Mappings by Transformation Type:
  TicketsToIssue           : 1250 mappings
  ContactToPersonne        : 450 mappings
  UtilisateurToPersonne    : 125 mappings
  ClientToCommanditaire    : 75 mappings
  PoleToDomaineMetier      : 12 mappings
Total Active Mappings: 1912
==========================================
```

## Benefits

1. **Complete Traceability**: Every transformation is tracked
2. **Data Integrity**: Verify that transformations are working correctly
3. **Performance**: Avoid re-transforming already processed entities
4. **Debugging**: Quickly identify which source record caused issues
5. **Reporting**: Generate comprehensive transformation reports
6. **Incremental Processing**: Support for delta/incremental ETL runs

## Database Queries for Analysis

### Find all tickets that were successfully transformed
```sql
SELECT t.source_entity_id as ticket_id, t.target_entity_oid as issue_oid
FROM transformation_mappings t
WHERE t.source_entity_type = 'Tickets' 
  AND t.target_entity_type = 'IssueInformatique'
  AND t.status = 'ACTIVE';
```

### Find contacts that became multiple personnes (potential duplicates)
```sql
SELECT source_entity_id, COUNT(*) as person_count
FROM transformation_mappings 
WHERE source_entity_type = 'Contact' 
  AND target_entity_type = 'Personne'
  AND status = 'ACTIVE'
GROUP BY source_entity_id
HAVING COUNT(*) > 1;
```

### Get transformation summary by type
```sql
SELECT 
    transformation_type,
    COUNT(*) as total_mappings,
    MIN(created_at) as first_transformation,
    MAX(created_at) as last_transformation
FROM transformation_mappings 
WHERE status = 'ACTIVE'
GROUP BY transformation_type;
```

The transformation mapping system provides complete visibility into your ETL process and ensures data integrity throughout the migration.
